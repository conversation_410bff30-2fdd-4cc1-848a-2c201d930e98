#!/usr/bin/env python3
"""
Test script for the Merchant URL Client with full data transformation.
This demonstrates the complete data flow from fetching to transformation.
"""

import sys
import os
import json
from datetime import datetime, timedelta
from typing import Dict, Any, List, Set
import re

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from dags.dependencies.merchant_url.merchant_url_client import MerchantURLClient
from dags.dependencies.merchant_url.models import (
    MerchantURLCreationLog,
    MerchantURLSuccessLog,
    MerchantURLStats
)

def extract_url_from_message(message: str, pattern: str) -> str:
    """Extract URL from log message using pattern."""
    if pattern in message:
        start = message.find(pattern) + len(pattern)
        # Find the end of the URL (comma, space, or end of string)
        end = len(message)
        for delimiter in [',', ' ', '###']:
            pos = message.find(delimiter, start)
            if pos != -1 and pos < end:
                end = pos
        return message[start:end].strip()
    return ""

def extract_domain_from_url(url: str) -> str:
    """Extract domain from URL."""
    if not url:
        return ""
    # Simple regex to extract domain
    match = re.search(r'https?://([^/]+)', url)
    return match.group(1) if match else ""

def parse_log_entry_enhanced(log_entry: Dict[str, Any]) -> Dict[str, Any]:
    """Enhanced parsing to extract data from message field."""
    parsed = log_entry.copy()
    
    message = log_entry.get('message', '')
    
    # Extract URLs from message
    if "Creating merchant URL" in message:
        # Extract the main URL
        url = extract_url_from_message(message, "url: ")
        parsed['url'] = url
        parsed['domain'] = extract_domain_from_url(url)
        
        # Extract other fields that might be in the message
        if "productId: " in message:
            parsed['product_id'] = extract_url_from_message(message, "productId: ")
        if "phiaId: " in message:
            parsed['user_id'] = extract_url_from_message(message, "phiaId: ")
            
    elif "Successfully created merchant URL" in message:
        # Extract original URL
        original_url = extract_url_from_message(message, "original url: ")
        parsed['url'] = original_url
        parsed['domain'] = extract_domain_from_url(original_url)
        
        # Extract merchant URL
        merchant_url = extract_url_from_message(message, "merchant product url: ")
        parsed['merchant_url'] = merchant_url
    
    return parsed

def transform_data(creating_logs: List[Dict], success_logs: List[Dict], date: str) -> Dict[str, Any]:
    """Transform raw logs into structured data - mimics the DAG transformation."""
    print("\n=== Data Transformation Process ===")
    print("-" * 50)
    
    # Parse and enhance log entries
    enhanced_creating_logs = [parse_log_entry_enhanced(log) for log in creating_logs[:5]]  # Sample 5
    enhanced_success_logs = [parse_log_entry_enhanced(log) for log in success_logs[:5]]  # Sample 5
    
    # Transform to model objects
    creation_logs = [
        MerchantURLCreationLog.from_dict(log) 
        for log in enhanced_creating_logs
    ]
    
    success_logs_models = [
        MerchantURLSuccessLog.from_dict(log)
        for log in enhanced_success_logs
    ]
    
    # Calculate statistics (using full dataset)
    unique_domains_creating = set()
    unique_users_creating = set()
    
    for log in creating_logs:
        parsed = parse_log_entry_enhanced(log)
        if parsed.get('domain'):
            unique_domains_creating.add(parsed['domain'])
        if parsed.get('user_id'):
            unique_users_creating.add(parsed['user_id'])
    
    unique_domains_success = set()
    unique_users_success = set()
    
    for log in success_logs:
        parsed = parse_log_entry_enhanced(log)
        if parsed.get('domain'):
            unique_domains_success.add(parsed['domain'])
        if parsed.get('user_id'):
            unique_users_success.add(parsed['user_id'])
    
    # Calculate overall stats
    total_creation_attempts = len(creating_logs)
    total_successful_creations = len(success_logs)
    success_rate = (
        total_successful_creations / total_creation_attempts 
        if total_creation_attempts > 0 
        else 0.0
    )
    
    stats = MerchantURLStats(
        date=date,
        total_creation_attempts=total_creation_attempts,
        total_successful_creations=total_successful_creations,
        success_rate=success_rate,
        unique_domains=len(unique_domains_creating.union(unique_domains_success)),
        unique_users=len(unique_users_creating.union(unique_users_success))
    )
    
    print(f"📊 Transformation Statistics:")
    print(f"   - Creation attempts: {total_creation_attempts}")
    print(f"   - Successful creations: {total_successful_creations}")
    print(f"   - Success rate: {success_rate:.2%}")
    print(f"   - Unique domains: {stats.unique_domains}")
    print(f"   - Unique users: {stats.unique_users}")
    
    return {
        "creation_logs": [log.to_dict() for log in creation_logs],
        "success_logs": [log.to_dict() for log in success_logs_models],
        "stats": stats.to_dict(),
        "sample_domains": list(unique_domains_creating)[:5],
        "sample_users": list(unique_users_creating)[:5]
    }

def show_supabase_data(transformed_data: Dict[str, Any]):
    """Show how data will be stored in Supabase."""
    print("\n=== Data to be Stored in Supabase ===")
    print("-" * 50)
    
    # Show sample creation log
    if transformed_data["creation_logs"]:
        print("\n1. Sample record for 'merchant_url_creation_logs' table:")
        sample = transformed_data["creation_logs"][0]
        print(json.dumps({
            "timestamp": sample["timestamp"],
            "insert_id": sample["insert_id"],
            "message": sample["message"][:100] + "...",
            "severity": sample["severity"],
            "url": sample.get("url", ""),
            "domain": sample.get("domain", ""),
            "user_id": sample.get("user_id", ""),
            "json_payload": "{...}"  # Abbreviated
        }, indent=2))
    
    # Show sample success log
    if transformed_data["success_logs"]:
        print("\n2. Sample record for 'merchant_url_success_logs' table:")
        sample = transformed_data["success_logs"][0]
        print(json.dumps({
            "timestamp": sample["timestamp"],
            "insert_id": sample["insert_id"],
            "message": sample["message"][:100] + "...",
            "severity": sample["severity"],
            "url": sample.get("url", ""),
            "merchant_url": sample.get("merchant_url", "")[:50] + "..." if sample.get("merchant_url") else "",
            "domain": sample.get("domain", ""),
            "json_payload": "{...}"  # Abbreviated
        }, indent=2))
    
    # Show stats record
    print("\n3. Record for 'merchant_url_stats' table:")
    print(json.dumps(transformed_data["stats"], indent=2))
    
    # Show sample domains and users
    print("\n4. Sample extracted data:")
    print(f"   - Domains: {transformed_data['sample_domains']}")
    print(f"   - Users: {transformed_data['sample_users']}")

def test_merchant_url_client_with_transformation():
    """Test the complete data flow from fetch to transformation."""
    print("=== Testing Merchant URL Client with Full Transformation ===\n")
    
    # Initialize the client
    client = MerchantURLClient()
    
    # Use yesterday's date
    yesterday = datetime.now() - timedelta(days=1)
    test_date = yesterday.strftime("%Y-%m-%d")
    
    print(f"Testing with date: {test_date}\n")
    
    try:
        # Fetch logs
        print("1. Fetching logs from Google Cloud Logging...")
        print("-" * 50)
        creating_logs = client.fetch_creating_merchant_urls(test_date)
        success_logs = client.fetch_successful_merchant_urls(test_date)
        
        print(f"✅ Fetched {len(creating_logs)} 'Creating merchant URL' logs")
        print(f"✅ Fetched {len(success_logs)} 'Successfully created merchant URL' logs")
        
        # Show raw log structure
        if creating_logs:
            print("\n2. Raw log structure example:")
            print("-" * 50)
            sample = creating_logs[0]
            print("Creating log fields:", list(sample.keys()))
            print(f"Message preview: {sample.get('message', '')[:200]}...")
        
        # Transform data
        transformed_data = transform_data(creating_logs, success_logs, test_date)
        
        # Show Supabase data
        show_supabase_data(transformed_data)
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    test_merchant_url_client_with_transformation()