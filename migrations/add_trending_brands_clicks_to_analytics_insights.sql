-- Migration: Update analytics_insights data structure for click-based analytics
-- Description: Documents the new data structure after removing search-based queries
--              and adding click-based trending brands and products data
-- Date: 2025-01-24

-- Since the 'data' column is already JSONB and contains nested objects,
-- we don't need to add new columns. The new click-based data will be stored
-- as keys within the existing 'data' JSONB column.

-- This migration is informational only - no schema changes needed.
-- The updated data structure will be:
-- {
--   "style_showdowns": [
--     {
--       "category": "color|material|style",
--       "comparison_type": "Black vs White",
--       "comparison": "Black is 25.5% more popular than White",
--       "metrics": "(1250 vs. 998 searches)",
--       "insight": "Monochrome is still queen — but black is reigning supreme.",
--       "primary_value": 1250,
--       "secondary_value": 998,
--       "analysis_date": "2025-01-24"
--     },
--     ...
--   ],
--   "trending_brands_clicks": [
--     {
--       "hostname": "shopbop.com",
--       "item_count": 1847
--     },
--     {
--       "hostname": "nordstrom.com", 
--       "item_count": 1203
--     },
--     ...
--   ],
--   "trending_product_clicks": [
--     {
--       "url": "https://shopbop.com/products/designer-handbag",
--       "view_count": 245
--     },
--     {
--       "url": "https://nordstrom.com/products/luxury-dress",
--       "view_count": 198
--     },
--     ...
--   ],
--   "metadata": {
--     "analysis_date": "2025-01-24",
--     "lookback_days": 7,
--     "total_showdowns": 6,
--     "total_brands_clicks": 2000,
--     "total_product_clicks": 5000,
--     "generated_at": "2025-01-24T10:30:00.000Z"
--   }
-- }

-- Query examples after implementation:

-- Get trending brand domains by clicks:
-- SELECT 
--   insight_date,
--   jsonb_array_length(data->'trending_brands_clicks') as brand_count,
--   data->'trending_brands_clicks' as trending_brands_clicks
-- FROM analytics_insights
-- WHERE insight_type = 'daily_trending'
--   AND data ? 'trending_brands_clicks'
-- ORDER BY insight_date DESC;

-- Get trending product URLs by clicks:
-- SELECT 
--   insight_date,
--   jsonb_array_length(data->'trending_product_clicks') as product_count,
--   data->'trending_product_clicks' as trending_product_clicks
-- FROM analytics_insights
-- WHERE insight_type = 'daily_trending'
--   AND data ? 'trending_product_clicks'
-- ORDER BY insight_date DESC;

-- Get style showdowns data:
-- SELECT 
--   insight_date,
--   jsonb_array_length(data->'style_showdowns') as showdown_count,
--   data->'style_showdowns' as style_showdowns
-- FROM analytics_insights
-- WHERE insight_type = 'daily_trending'
--   AND data ? 'style_showdowns'
-- ORDER BY insight_date DESC;

-- Get top 10 domains by clicks for a specific date:
-- SELECT 
--   brand_click->>'hostname' as hostname,
--   (brand_click->>'item_count')::int as item_count
-- FROM analytics_insights,
--      jsonb_array_elements(data->'trending_brands_clicks') as brand_click
-- WHERE insight_type = 'daily_trending'
--   AND insight_date = '2025-01-24'
-- ORDER BY (brand_click->>'item_count')::int DESC
-- LIMIT 10;