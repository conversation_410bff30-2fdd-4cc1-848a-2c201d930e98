-- Migration: Create merchant URL tables
-- Description: Tables for storing merchant URL creation logs from Google Cloud Logging

-- Create table for merchant URL creation logs
CREATE TABLE IF NOT EXISTS merchant_url_creation_logs (
    id BIGSERIAL PRIMARY KEY,
    timestamp TIMESTAMP WITH TIME ZONE NOT NULL,
    insert_id TEXT NOT NULL,
    message TEXT NOT NULL,
    severity TEXT,
    resource JSONB,
    labels JSONB,
    json_payload JSONB,
    url TEXT,
    merchant_url TEXT,
    domain TEXT,
    user_id TEXT,
    status TEXT,
    error TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(insert_id)
);

-- Create table for successful merchant URL logs
CREATE TABLE IF NOT EXISTS merchant_url_success_logs (
    id BIGSERIAL PRIMARY KEY,
    timestamp TIMESTAMP WITH TIME ZONE NOT NULL,
    insert_id TEXT NOT NULL,
    message TEXT NOT NULL,
    severity TEXT,
    resource JSONB,
    labels JSONB,
    json_payload JSONB,
    url TEXT,
    merchant_url TEXT,
    domain TEXT,
    user_id TEXT,
    status TEXT,
    error TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(insert_id)
);

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_merchant_url_creation_logs_timestamp 
    ON merchant_url_creation_logs(timestamp);
CREATE INDEX IF NOT EXISTS idx_merchant_url_creation_logs_domain 
    ON merchant_url_creation_logs(domain);
CREATE INDEX IF NOT EXISTS idx_merchant_url_creation_logs_user_id 
    ON merchant_url_creation_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_merchant_url_creation_logs_created_at 
    ON merchant_url_creation_logs(created_at);

CREATE INDEX IF NOT EXISTS idx_merchant_url_success_logs_timestamp 
    ON merchant_url_success_logs(timestamp);
CREATE INDEX IF NOT EXISTS idx_merchant_url_success_logs_domain 
    ON merchant_url_success_logs(domain);
CREATE INDEX IF NOT EXISTS idx_merchant_url_success_logs_user_id 
    ON merchant_url_success_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_merchant_url_success_logs_created_at 
    ON merchant_url_success_logs(created_at);

-- Add comments to tables
COMMENT ON TABLE merchant_url_creation_logs IS 'Logs for merchant URL creation attempts from Google Cloud Logging';
COMMENT ON TABLE merchant_url_success_logs IS 'Logs for successfully created merchant URLs from Google Cloud Logging';

-- Add comments to columns
COMMENT ON COLUMN merchant_url_creation_logs.insert_id IS 'Unique identifier from Google Cloud Logging';
COMMENT ON COLUMN merchant_url_creation_logs.json_payload IS 'Full JSON payload from the log entry';
COMMENT ON COLUMN merchant_url_creation_logs.url IS 'Original URL that was being converted';
COMMENT ON COLUMN merchant_url_creation_logs.domain IS 'Domain extracted from the URL';

COMMENT ON COLUMN merchant_url_success_logs.insert_id IS 'Unique identifier from Google Cloud Logging';
COMMENT ON COLUMN merchant_url_success_logs.json_payload IS 'Full JSON payload from the log entry';
COMMENT ON COLUMN merchant_url_success_logs.url IS 'Original URL that was converted';
COMMENT ON COLUMN merchant_url_success_logs.merchant_url IS 'Generated merchant/affiliate URL';
COMMENT ON COLUMN merchant_url_success_logs.domain IS 'Domain extracted from the URL';