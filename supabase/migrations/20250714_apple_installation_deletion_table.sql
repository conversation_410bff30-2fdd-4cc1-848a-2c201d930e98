-- Migration: Create Apple Installation/Deletion Data Table
-- This migration creates a simplified table to store Apple App Store Installation and Deletion report data

-- Create apple_installation_deletion_data table
CREATE TABLE IF NOT EXISTS apple_installation_deletion_data (
    -- Primary key and identifiers
    unique_id VARCHAR(32) PRIMARY KEY,
    
    -- Core data fields (required)
    date DATE NOT NULL,
    app_name VARCHAR(255) NOT NULL,
    app_apple_identifier VARCHAR(20) NOT NULL,
    event VARCHAR(50) NOT NULL,
    app_version VARCHAR(50) NOT NULL,
    device VARCHAR(50) NOT NULL,
    platform_version VARCHAR(50) NOT NULL,
    source_type VARCHAR(100) NOT NULL,
    territory CHAR(2) NOT NULL,
    counts INTEGER NOT NULL CHECK (counts >= 0),
    unique_devices INTEGER NOT NULL CHECK (unique_devices >= 0),
    
    -- Optional fields
    download_type VARCHAR(100),
    page_type VARCHAR(100),
    app_download_date DATE,
    
    -- <PERSON><PERSON><PERSON>
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- <PERSON>reate indexes for performance
CREATE INDEX IF NOT EXISTS idx_apple_installation_deletion_date ON apple_installation_deletion_data(date);
CREATE INDEX IF NOT EXISTS idx_apple_installation_deletion_app_id ON apple_installation_deletion_data(app_apple_identifier);
CREATE INDEX IF NOT EXISTS idx_apple_installation_deletion_event ON apple_installation_deletion_data(event);
CREATE INDEX IF NOT EXISTS idx_apple_installation_deletion_territory ON apple_installation_deletion_data(territory);
CREATE INDEX IF NOT EXISTS idx_apple_installation_deletion_device ON apple_installation_deletion_data(device);
CREATE INDEX IF NOT EXISTS idx_apple_installation_deletion_source_type ON apple_installation_deletion_data(source_type);
CREATE INDEX IF NOT EXISTS idx_apple_installation_deletion_created_at ON apple_installation_deletion_data(created_at);

-- Composite indexes for common queries
CREATE INDEX IF NOT EXISTS idx_apple_installation_deletion_app_date ON apple_installation_deletion_data(app_apple_identifier, date);
CREATE INDEX IF NOT EXISTS idx_apple_installation_deletion_event_date ON apple_installation_deletion_data(event, date);
CREATE INDEX IF NOT EXISTS idx_apple_installation_deletion_territory_date ON apple_installation_deletion_data(territory, date);

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_apple_installation_deletion_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger to automatically update updated_at
CREATE TRIGGER update_apple_installation_deletion_updated_at 
    BEFORE UPDATE ON apple_installation_deletion_data 
    FOR EACH ROW 
    EXECUTE FUNCTION update_apple_installation_deletion_updated_at();

-- Create view for daily summary
CREATE OR REPLACE VIEW apple_installation_deletion_daily_summary AS
SELECT 
    date,
    app_apple_identifier,
    app_name,
    event,
    territory,
    device,
    COUNT(*) as record_count,
    SUM(counts) as total_counts,
    SUM(unique_devices) as total_unique_devices,
    AVG(counts) as avg_counts,
    AVG(unique_devices) as avg_unique_devices
FROM apple_installation_deletion_data
GROUP BY date, app_apple_identifier, app_name, event, territory, device
ORDER BY date DESC, total_counts DESC;

-- Create view for app summary
CREATE OR REPLACE VIEW apple_installation_deletion_app_summary AS
SELECT 
    app_apple_identifier,
    app_name,
    COUNT(*) as total_records,
    COUNT(CASE WHEN event = 'Delete' THEN 1 END) as delete_records,
    COUNT(CASE WHEN event != 'Delete' THEN 1 END) as install_records,
    SUM(counts) as total_counts,
    SUM(unique_devices) as total_unique_devices,
    MIN(date) as first_report_date,
    MAX(date) as last_report_date,
    COUNT(DISTINCT territory) as territories_count,
    COUNT(DISTINCT device) as device_types_count
FROM apple_installation_deletion_data
GROUP BY app_apple_identifier, app_name
ORDER BY total_counts DESC;

-- Row Level Security (RLS) setup
ALTER TABLE apple_installation_deletion_data ENABLE ROW LEVEL SECURITY;

-- Create policy for authenticated users (read-only)
CREATE POLICY "Allow authenticated users to read apple installation deletion data" ON apple_installation_deletion_data
    FOR SELECT USING (auth.role() = 'authenticated');

-- Create policy for service role (full access for DAG operations)
CREATE POLICY "Allow service role full access to apple installation deletion data" ON apple_installation_deletion_data
    FOR ALL USING (auth.role() = 'service_role');

-- Grant permissions
GRANT SELECT ON apple_installation_deletion_data TO authenticated;
GRANT SELECT ON apple_installation_deletion_daily_summary TO authenticated;
GRANT SELECT ON apple_installation_deletion_app_summary TO authenticated;

-- Grant full access to service role for DAG operations
GRANT ALL ON apple_installation_deletion_data TO service_role;

-- Add comments for documentation
COMMENT ON TABLE apple_installation_deletion_data IS 'Apple App Store Installation and Deletion report data';
COMMENT ON COLUMN apple_installation_deletion_data.unique_id IS 'Generated unique identifier based on all distinguishing fields';
COMMENT ON COLUMN apple_installation_deletion_data.date IS 'Report date from Apple';
COMMENT ON COLUMN apple_installation_deletion_data.app_apple_identifier IS 'Apple App Store ID';
COMMENT ON COLUMN apple_installation_deletion_data.event IS 'Event type (Install, Delete, etc.)';
COMMENT ON COLUMN apple_installation_deletion_data.counts IS 'Number of events';
COMMENT ON COLUMN apple_installation_deletion_data.unique_devices IS 'Number of unique devices';