-- Create onboarding_analytics table for storing daily onboarding and Safari permissions metrics
-- This table stores JSON analytics data similar to the analytics_insights table

CREATE TABLE IF NOT EXISTS onboarding_analytics (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    analysis_date DATE NOT NULL,
    insight_type TEXT NOT NULL DEFAULT 'daily_onboarding',
    data JSONB NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create index on analysis_date for efficient querying
CREATE INDEX IF NOT EXISTS idx_onboarding_analytics_analysis_date 
ON onboarding_analytics (analysis_date);

-- <PERSON>reate index on insight_type for filtering
CREATE INDEX IF NOT EXISTS idx_onboarding_analytics_insight_type 
ON onboarding_analytics (insight_type);

-- Create unique constraint to prevent duplicate entries for same date/type
CREATE UNIQUE INDEX IF NOT EXISTS idx_onboarding_analytics_unique_date_type 
ON onboarding_analytics (analysis_date, insight_type);

-- <PERSON>reate trigger to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_onboarding_analytics_updated_at 
    BEFORE UPDATE ON onboarding_analytics 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Add comment describing the table purpose
COMMENT ON TABLE onboarding_analytics IS 'Daily onboarding completion and Safari permissions analytics metrics from Mixpanel data';
COMMENT ON COLUMN onboarding_analytics.analysis_date IS 'Date of the analytics analysis';
COMMENT ON COLUMN onboarding_analytics.insight_type IS 'Type of insight (daily_onboarding)';
COMMENT ON COLUMN onboarding_analytics.data IS 'JSON data containing onboarding completion and Safari permissions metrics';