#!/usr/bin/env python
"""
End-to-End Test for onboarding_analytics_dag

This script simulates a complete DAG run locally:
1. Runs both BigQuery tasks individually 
2. Collects their results
3. Passes results to Supabase push function
4. Verifies data integrity throughout the pipeline

Usage: python test_onboarding_dag_end_to_end.py [--execution-date YYYY-MM-DD]
"""

import sys
import os
import argparse
from datetime import datetime, timedelta
import json
import logging

# Load environment variables from .env file
try:
    from dotenv import load_dotenv
    load_dotenv()
    print("✓ Loaded environment variables from .env file")
except ImportError:
    print("⚠ python-dotenv not installed, using system environment variables")

# Add dags folder to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'dags'))

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def create_mock_context(execution_date: datetime):
    """Create a mock Airflow context for testing"""
    ds = execution_date.strftime('%Y-%m-%d')
    
    class MockTaskInstance:
        def __init__(self):
            self.results = {}
        
        def xcom_pull(self, task_ids):
            return self.results.get(task_ids)
        
        def xcom_push(self, task_id, value):
            self.results[task_id] = value
    
    task_instance = MockTaskInstance()
    
    context = {
        'ds': ds,
        'execution_date': execution_date,
        'task_instance': task_instance
    }
    
    return context, task_instance

def validate_onboarding_results(results, query_name):
    """Validate the structure and content of onboarding query results"""
    logger.info(f"Validating {query_name} results...")
    
    if not results:
        logger.warning(f"{query_name}: No results returned")
        return False
    
    if not isinstance(results, list):
        logger.error(f"{query_name}: Results should be a list, got {type(results)}")
        return False
    
    logger.info(f"{query_name}: ✓ {len(results)} records")
    
    if len(results) > 0:
        sample = results[0]
        logger.info(f"{query_name}: ✓ Sample keys: {list(sample.keys())}")
        
        # Check for required fields based on query type
        if query_name == "onboarding_completion":
            required_fields = ['app_first_run_users', 'page_view_users', 'percent_onboarded_with_notifications']
            for field in required_fields:
                if field not in sample:
                    logger.error(f"{query_name}: Missing required field: {field}")
                    return False
                else:
                    value = sample[field]
                    logger.info(f"  - {field}: {value}")
                    if field.startswith('percent_') and value is not None:
                        if not (0 <= value <= 100):
                            logger.warning(f"  - {field}: Percentage value {value} outside expected range 0-100")
        
        elif query_name == "safari_permissions":
            required_fields = ['step1_users', 'converted_users', 'percent_converted']
            for field in required_fields:
                if field not in sample:
                    logger.error(f"{query_name}: Missing required field: {field}")
                    return False
                else:
                    value = sample[field]
                    logger.info(f"  - {field}: {value}")
                    if field.startswith('percent_') and value is not None:
                        if not (0 <= value <= 100):
                            logger.warning(f"  - {field}: Percentage value {value} outside expected range 0-100")
        
        logger.info(f"{query_name}: ✓ All expected fields present and valid")
    
    return True

def test_bigquery_connectivity():
    """Test BigQuery connection and basic query"""
    logger.info("Testing BigQuery connectivity...")
    try:
        from google.cloud import bigquery
        client = bigquery.Client(project='phia-prod-416420')
        
        # Test basic connectivity
        test_query = "SELECT 1 as test, CURRENT_TIMESTAMP() as timestamp"
        query_job = client.query(test_query)
        result = list(query_job.result())
        
        logger.info(f"✓ BigQuery connected: {result[0].timestamp}")
        
        # Test access to mixpanel dataset
        test_mixpanel_query = """
        SELECT COUNT(*) as total_events 
        FROM `phia-prod-416420.mixpanel.app_first_run` 
        WHERE DATE(time) >= DATE_SUB(CURRENT_DATE(), INTERVAL 30 DAY)
        LIMIT 1
        """
        query_job = client.query(test_mixpanel_query)
        result = list(query_job.result())
        logger.info(f"✓ Mixpanel dataset accessible: {result[0].total_events} recent app_first_run events")
        
        return True
    except Exception as e:
        logger.error(f"✗ BigQuery connection failed: {e}")
        return False

def test_supabase_connectivity():
    """Test Supabase connection"""
    logger.info("Testing Supabase connectivity...")
    try:
        from supabase import create_client
        
        # Get credentials from environment (.env file)
        supabase_url = os.getenv("SUPABASE_URL")
        supabase_key = os.getenv("SUPABASE_SERVICE_ROLE_KEY")  # Use service role key for testing
        
        if not supabase_url or not supabase_key:
            logger.warning("⚠ Supabase credentials not configured in .env file")
            logger.info("Expected: SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY")
            return False
        
        logger.info(f"Using Supabase URL: {supabase_url}")
        
        # Test connection
        supabase = create_client(supabase_url, supabase_key)
        
        # Try a simple read operation to test connectivity
        try:
            result = supabase.table('onboarding_analytics').select("analysis_date").limit(1).execute()
            logger.info("✓ Supabase connected successfully")
            logger.info(f"✓ Found {len(result.data)} existing onboarding analytics records")
            return True
        except Exception as e:
            logger.warning(f"⚠ Supabase connection works but table access failed: {e}")
            logger.info("This might be expected if the onboarding_analytics table doesn't exist yet")
            logger.info("You may need to run the migration: supabase/migrations/create_onboarding_analytics_table.sql")
            return True  # Still consider connection successful
            
    except Exception as e:
        logger.error(f"✗ Supabase connection failed: {e}")
        return False

def run_end_to_end_test(execution_date: datetime, test_supabase: bool = True):
    """Run the complete end-to-end test"""
    logger.info("="*60)
    logger.info(f"Starting End-to-End Test for Onboarding Analytics DAG")
    logger.info(f"Execution Date: {execution_date.strftime('%Y-%m-%d')}")
    logger.info("="*60)
    
    # Pre-flight checks
    logger.info("\n🔍 STEP 1: Pre-flight Connectivity Tests")
    logger.info("-" * 40)
    
    if not test_bigquery_connectivity():
        logger.error("❌ BigQuery connectivity failed - cannot proceed")
        return False
    
    supabase_available = test_supabase_connectivity() if test_supabase else False
    if test_supabase and not supabase_available:
        logger.warning("⚠ Supabase not available - will test queries only")
    
    # Import task functions
    logger.info("\n📦 STEP 2: Loading Task Functions")
    logger.info("-" * 40)
    
    try:
        from dependencies.onboarding.onboarding_tasks import (
            run_onboarding_completion_query,
            run_safari_permissions_query,
            push_onboarding_to_supabase
        )
        logger.info("✓ All task functions loaded successfully")
    except Exception as e:
        logger.error(f"❌ Failed to import task functions: {e}")
        logger.error("Make sure the onboarding module is properly created in dags/dependencies/onboarding/")
        return False
    
    # Create mock context
    context, task_instance = create_mock_context(execution_date)
    
    # Run all query tasks
    logger.info("\n🔄 STEP 3: Running BigQuery Tasks")
    logger.info("-" * 40)
    
    query_results = {}
    query_tasks = [
        ("onboarding_completion", run_onboarding_completion_query, "get_onboarding_completion"),
        ("safari_permissions", run_safari_permissions_query, "get_safari_permissions")
    ]
    
    for query_name, query_func, task_id in query_tasks:
        logger.info(f"\nRunning {query_name} query...")
        try:
            start_time = datetime.now()
            results = query_func(**context)
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            logger.info(f"✓ {query_name} completed in {duration:.2f}s")
            
            # Validate results
            if validate_onboarding_results(results, query_name):
                query_results[query_name] = results
                # Store in mock task instance for Supabase task
                task_instance.xcom_push(task_id, results)
            else:
                logger.error(f"❌ {query_name} validation failed")
                return False
                
        except Exception as e:
            logger.error(f"❌ {query_name} failed: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return False
    
    # Analyze results
    logger.info("\n📊 STEP 4: Results Analysis")
    logger.info("-" * 40)
    
    total_records = sum(len(results) for results in query_results.values())
    logger.info(f"Total records retrieved: {total_records}")
    
    for query_name, results in query_results.items():
        logger.info(f"  - {query_name}: {len(results)} records")
        if results and len(results) > 0:
            sample = results[0]
            
            if query_name == "onboarding_completion":
                app_users = sample.get('app_first_run_users', 0)
                page_users = sample.get('page_view_users', 0)
                completion_rate = sample.get('percent_onboarded_with_notifications', 0)
                logger.info(f"    App first run users: {app_users}")
                logger.info(f"    Onboarding completions: {page_users}")
                logger.info(f"    Completion rate: {completion_rate:.2f}%")
                
            elif query_name == "safari_permissions":
                step1_users = sample.get('step1_users', 0)
                converted_users = sample.get('converted_users', 0)
                conversion_rate = sample.get('percent_converted', 0)
                logger.info(f"    Users started Safari setup: {step1_users}")
                logger.info(f"    Users completed setup: {converted_users}")
                logger.info(f"    Conversion rate: {conversion_rate:.2f}%")
    
    # Test Supabase push (if available)
    if test_supabase and supabase_available:
        logger.info("\n📤 STEP 5: Testing Supabase Push")
        logger.info("-" * 40)
        
        try:
            logger.info("Running Supabase push function...")
            start_time = datetime.now()
            push_result = push_onboarding_to_supabase(**context)
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            logger.info(f"✓ Supabase push completed in {duration:.2f}s")
            logger.info(f"✓ Push result: {push_result}")
            
            # Verify data was actually written
            logger.info("Verifying data in Supabase...")
            from supabase import create_client
            
            supabase_url = os.getenv("SUPABASE_URL")
            supabase_key = os.getenv("SUPABASE_SERVICE_ROLE_KEY")
            
            supabase = create_client(supabase_url, supabase_key)
            
            # Check if our data exists
            ds = execution_date.strftime('%Y-%m-%d')
            verification_query = supabase.table('onboarding_analytics').select("*").eq('analysis_date', ds).eq('insight_type', 'daily_onboarding')
            verification_result = verification_query.execute()
            
            if verification_result.data:
                logger.info("✓ Data successfully written and verified in Supabase")
                insight_data = verification_result.data[0]['data']
                
                # Verify onboarding completion data
                onboarding_data = insight_data.get('onboarding_completion')
                if onboarding_data:
                    logger.info(f"✓ Onboarding completion rate: {onboarding_data.get('percent_onboarded_with_notifications', 'N/A')}%")
                
                # Verify safari permissions data
                safari_data = insight_data.get('safari_permissions')
                if safari_data:
                    logger.info(f"✓ Safari permissions conversion rate: {safari_data.get('percent_converted', 'N/A')}%")
                    
            else:
                logger.warning("⚠ Data push succeeded but verification query found no matching records")
            
        except Exception as e:
            logger.error(f"❌ Supabase push failed: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return False
    else:
        logger.info("\n⏭️  STEP 5: Skipping Supabase Push Test")
        logger.info("-" * 40)
        logger.info("Supabase test skipped (not available or not requested)")
    
    # Final summary
    logger.info("\n🎉 STEP 6: Test Summary")
    logger.info("-" * 40)
    logger.info("✅ END-TO-END TEST COMPLETED SUCCESSFULLY!")
    logger.info(f"✅ Execution Date: {execution_date.strftime('%Y-%m-%d')}")
    logger.info(f"✅ Total Records Processed: {total_records}")
    logger.info("✅ All BigQuery tasks executed successfully")
    if test_supabase and supabase_available:
        logger.info("✅ Supabase integration verified")
    logger.info("✅ Onboarding analytics pipeline is working correctly")
    
    # Display key metrics summary
    if query_results:
        logger.info("\n📈 Key Metrics Summary:")
        logger.info("-" * 40)
        
        if 'onboarding_completion' in query_results and query_results['onboarding_completion']:
            onboarding = query_results['onboarding_completion'][0]
            completion_rate = onboarding.get('percent_onboarded_with_notifications', 0)
            logger.info(f"📱 Onboarding Completion Rate: {completion_rate:.1f}%")
            
        if 'safari_permissions' in query_results and query_results['safari_permissions']:
            safari = query_results['safari_permissions'][0]
            conversion_rate = safari.get('percent_converted', 0)
            logger.info(f"🔒 Safari Permissions Conversion Rate: {conversion_rate:.1f}%")
    
    return True

def main():
    parser = argparse.ArgumentParser(description='End-to-end test for onboarding analytics DAG')
    parser.add_argument('--execution-date', 
                       default=(datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d'),
                       help='Execution date in YYYY-MM-DD format (default: yesterday)')
    parser.add_argument('--skip-supabase', action='store_true',
                       help='Skip Supabase connectivity and push tests')
    
    args = parser.parse_args()
    
    try:
        execution_date = datetime.strptime(args.execution_date, '%Y-%m-%d')
    except ValueError:
        logger.error("Invalid date format. Use YYYY-MM-DD")
        sys.exit(1)
    
    test_supabase = not args.skip_supabase
    
    # Run the test
    success = run_end_to_end_test(execution_date, test_supabase)
    
    if success:
        logger.info("\n🚀 READY FOR PRODUCTION DEPLOYMENT!")
        logger.info("The onboarding analytics DAG is working correctly and can be deployed to Airflow.")
        sys.exit(0)
    else:
        logger.error("\n❌ TEST FAILED!")
        logger.error("Please fix the issues before deploying to production.")
        sys.exit(1)

if __name__ == "__main__":
    main()