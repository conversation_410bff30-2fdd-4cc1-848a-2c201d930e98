"""
Trending Products Analytics DAG

Daily analytics pipeline that processes Mixpanel engagement data in BigQuery
and pushes aggregated insights to Supabase for dashboard consumption.

Author: Data Team
Created: 2024-01-21
"""

from airflow import DAG
from airflow.operators.python import PythonOperator
from datetime import datetime, timedelta
from dependencies.trending.trending_tasks import (
    run_style_showdowns_query,
    run_trending_brands_clicks_query,
    run_trending_product_clicks_query,
    push_all_to_supabase
)

default_args = {
    'owner': 'data-team',
    'depends_on_past': False,
    'email_on_failure': True,
    'email_on_retry': False,
    'retries': 2,
    'retry_delay': timedelta(minutes=5),
    'email': ['<EMAIL>'],
}

# DAG definition
with DAG(
    dag_id="trending_products_analytics",
    default_args=default_args,
    description="Daily analytics pipeline using direct BigQuery execution",
    schedule="0 3 * * *",
    start_date=datetime(2024, 1, 21),
    catchup=False,
    tags=["bigquery", "analytics", "mixpanel", "supabase", "daily"],
    max_active_runs=1,
) as dag:
    
    # Task 1: Get style showdowns
    get_style_showdowns = PythonOperator(
        task_id="get_style_showdowns",
        python_callable=run_style_showdowns_query,
    )
    
    # Task 2: Get trending brands clicks
    get_trending_brands_clicks = PythonOperator(
        task_id="get_trending_brands_clicks",
        python_callable=run_trending_brands_clicks_query,
    )
    
    # Task 3: Get trending product clicks
    get_trending_product_clicks = PythonOperator(
        task_id="get_trending_product_clicks",
        python_callable=run_trending_product_clicks_query,
    )
    
    # Task 4: Push all results to Supabase
    push_to_supabase = PythonOperator(
        task_id="push_to_supabase",
        python_callable=push_all_to_supabase,
        trigger_rule='none_failed_or_skipped',
    )
    
    # Define dependencies - all queries can run in parallel, then push to Supabase
    [get_style_showdowns, get_trending_brands_clicks, get_trending_product_clicks] >> push_to_supabase