"""
Top Domain Clicks DAG - Fetches merchant URL creation logs from Google Cloud Logging.

This DAG:
1. Fetches "Creating merchant URL" logs
2. Fetches "Successfully created merchant URL" logs
3. Transforms the data
4. Stores results in Supabase tables (merchant_url_creation_logs, merchant_url_success_logs)

Production Configuration:
- Runs daily at 2 AM UTC
- Processes previous day's data
- Catchup enabled for historical data from April 21, 2025
- Retries enabled with exponential backoff
- Single active run to prevent resource conflicts
"""

from datetime import datetime, timedelta
from airflow import DAG
from airflow.models import Variable
from airflow.utils.task_group import TaskGroup

from dependencies.merchant_url.merchant_url_tasks import (
    fetch_merchant_url_logs,
    transform_merchant_url_data,
    load_to_supabase,
    validate_data_quality
)

# Get configuration from Airflow Variables (with defaults)
MERCHANT_URL_CONFIG = Variable.get("merchant_url_config", deserialize_json=True, default_var={
    "gcp_project_id": "phia-prod-416420",
    "supabase_conn_id": "supabase_conn",
    "email_on_failure": ["<EMAIL>"],
    "max_retries": 2,
    "retry_delay_minutes": 5
})

# Default arguments for the DAG
default_args = {
    "owner": "data-team",
    "depends_on_past": False,
    "email": MERCHANT_URL_CONFIG.get("email_on_failure", ["<EMAIL>"]),
    "email_on_failure": True,
    "email_on_retry": False,
    "retries": MERCHANT_URL_CONFIG.get("max_retries", 2),
    "retry_delay": timedelta(minutes=MERCHANT_URL_CONFIG.get("retry_delay_minutes", 5)),
    "execution_timeout": timedelta(hours=2),  # Max 2 hours per task
}

# Create the DAG
dag = DAG(
    "top_domain_clicks_dag",
    default_args=default_args,
    description="Fetch and store merchant URL creation logs from Google Cloud Logging",
    schedule_interval="0 2 * * *",  # Daily at 2 AM UTC
    start_date=datetime(2025, 4, 21),  # Start from the date mentioned in requirements
    catchup=True,  # Enable catchup to process historical data
    max_active_runs=1,  # Prevent concurrent runs
    max_active_tasks=4,  # Limit concurrent tasks
    tags=["merchant-urls", "logging", "gcp", "production"],
    doc_md=__doc__,
)

# Define the task flow
with dag:
    with TaskGroup("extract_and_transform") as extract_transform_group:
        # Fetch logs from Google Cloud Logging
        logs_data = fetch_merchant_url_logs()
        
        # Transform the raw logs into structured data
        transformed_data = transform_merchant_url_data(logs_data)
        
        logs_data >> transformed_data
    
    with TaskGroup("load_and_validate") as load_validate_group:
        # Load the data to Supabase
        load_results = load_to_supabase(transformed_data)
        
        # Validate data quality
        validation = validate_data_quality(load_results, transformed_data)
        
        load_results >> validation
    
    # Set task group dependencies
    extract_transform_group >> load_validate_group