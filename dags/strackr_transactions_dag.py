"""
Simplified Strackr Transaction Reports DAG

This DAG fetches transactions from Strackr API, transforms them to a normalized schema,
and stores them in Supabase.

Schedule: Daily at 2 AM UTC
Catchup: False (only process current data)
"""

from airflow import DAG
from airflow.operators.python import PythonOperator
from airflow.utils.task_group import TaskGroup
from datetime import datetime, timedelta
import logging

# Import our simplified functions
from dependencies.strackr import (
    fetch_strackr_transactions,
    transform_strackr_transactions,
    upload_to_supabase,
)

logger = logging.getLogger(__name__)

# Log DAG initialization
logger.info("=" * 80)
logger.info("🚀 INITIALIZING STRACKR TRANSACTIONS DAG")
logger.info("=" * 80)

# DAG Configuration
default_args = {
    "owner": "data-team",
    "depends_on_past": False,
    "start_date": datetime(2024, 1, 1),
    "email_on_failure": True,
    "email_on_retry": False,
    "retries": 2,
    "retry_delay": timed<PERSON>ta(minutes=5),
    "email": ["<EMAIL>"],
}

logger.info("📋 DAG Configuration:")
logger.info(f"   • Owner: {default_args['owner']}")
logger.info(f"   • Start Date: {default_args['start_date']}")
logger.info(f"   • Retries: {default_args['retries']}")
logger.info(f"   • Retry Delay: {default_args['retry_delay']}")
logger.info(f"   • Email Notifications: {default_args['email']}")

# Create the DAG
dag = DAG(
    "strackr_transaction_reports",
    default_args=default_args,
    description="Enhanced Strackr transaction processing with 7-day rolling window",
    schedule="0 2 * * *",  # Daily at 2 AM UTC
    start_date=datetime(2024, 1, 1),
    catchup=False,
    tags=["transaction", "strackr", "enhanced", "7-day-window"],
    max_active_runs=1,
)

logger.info("📊 DAG Settings:")
logger.info(f"   • DAG ID: {dag.dag_id}")
logger.info(f"   • Catchup: {dag.catchup}")
logger.info(f"   • Max Active Runs: {dag.max_active_runs}")
logger.info(f"   • Tags: {dag.tags}")


def log_task_start(task_name: str, **context):
    """Log task start with context information."""
    logger.info("=" * 80)
    logger.info(f"🏁 TASK START: {task_name}")
    logger.info("=" * 80)
    logger.info(f"   • Execution Date: {context.get('execution_date')}")
    logger.info(f"   • DAG Run ID: {context.get('run_id')}")
    logger.info(f"   • Task Instance: {context.get('task_instance')}")


def log_task_completion(task_name: str, result: dict, **context):
    """Log task completion with results."""
    success = result.get("success", False)
    execution_time = result.get("task_execution_time", 0)

    if success:
        logger.info("=" * 80)
        logger.info(f"✅ TASK COMPLETED: {task_name} in {execution_time:.2f} seconds")
        logger.info("=" * 80)
    else:
        logger.error("=" * 80)
        logger.error(f"❌ TASK FAILED: {task_name} after {execution_time:.2f} seconds")
        logger.error(f"   • Error: {result.get('error', 'Unknown error')}")
        logger.error("=" * 80)


# Task 1: Fetch transactions from Strackr API
fetch_task = PythonOperator(
    task_id="fetch_strackr_transactions",
    python_callable=fetch_strackr_transactions,
    dag=dag,
    doc_md="""
    ## Fetch Strackr Transactions

    Fetches transactions from Strackr API for the last 7 days (rolling window).

    **What it does:**
    - Gets 7-day date range ending yesterday
    - Calls Strackr API with authentication
    - Handles pagination automatically
    - Returns raw transaction data
    - Ensures delayed transactions are captured

    **7-Day Rolling Window Benefits:**
    - Captures delayed transaction reporting
    - Handles network-specific reporting schedules
    - Ensures data completeness and accuracy
    - Allows for transaction updates and corrections

    **Logging:**
    - API request/response details
    - Pagination progress
    - Transaction statistics
    - Performance metrics
    """,
)

# Task 2: Transform transactions to normalized format
transform_task = PythonOperator(
    task_id="transform_strackr_transactions",
    python_callable=transform_strackr_transactions,
    dag=dag,
    doc_md="""
    ## Transform Strackr Transactions

    Transforms raw Strackr data to normalized format using Pydantic models.

    **What it does:**
    - Takes raw transactions from fetch task
    - Validates and transforms each transaction
    - Filters out invalid transactions
    - Returns normalized transaction data

    **Logging:**
    - Transformation progress by batches
    - Validation errors and warnings
    - Data quality statistics
    - Performance metrics
    """,
)

# Task 3: Upload to Supabase
upload_task = PythonOperator(
    task_id="upload_to_supabase",
    python_callable=upload_to_supabase,
    dag=dag,
    doc_md="""
    ## Upload to Supabase

    Uploads normalized transactions to Supabase database with smart upsert logic.

    **What it does:**
    - Takes normalized transactions from transform task
    - Checks existing transactions for new vs update tracking
    - Uploads to Supabase in batches with smart upsert
    - Handles duplicates with conflict resolution on transaction_id
    - Provides detailed upload statistics including new/updated counts

    **Smart Upsert Benefits:**
    - Prevents duplicate transactions
    - Handles transaction updates (commission changes, status updates)
    - Provides visibility into data freshness
    - Ensures data consistency across 7-day rolling window

    **Logging:**
    - Batch upload progress with new/updated breakdown
    - Database connection details
    - Upload statistics and performance metrics
    - Data freshness validation
    - Error handling and recovery
    """,
)

# Define task dependencies - simple linear flow
fetch_task >> transform_task >> upload_task

logger.info("🔗 Task Dependencies Configured:")
logger.info(
    "   fetch_strackr_transactions >> transform_strackr_transactions >> upload_to_supabase"
)

# Add DAG documentation
dag.doc_md = """
# Enhanced Strackr Transaction DAG with 7-Day Rolling Window

This is an enhanced version of the Strackr transaction processing DAG with smart upsert logic and comprehensive data validation.

## Key Features

- **7-Day Rolling Window**: Fetches last 7 days to capture delayed transactions
- **Smart Upsert Logic**: Handles both new and updated transactions intelligently
- **Data Freshness Validation**: Tracks new vs updated records for monitoring
- **Comprehensive Logging**: Detailed logs for production debugging and monitoring
- **Simple Linear Flow**: fetch → transform → upload with enhanced error handling

## Data Flow

1. **Fetch**: Get last 7 days of transactions from Strackr API (rolling window)
2. **Transform**: Convert to normalized format with Pydantic validation
3. **Upload**: Store in Supabase with smart upsert and conflict resolution

## 7-Day Rolling Window Benefits

- **Captures Delayed Reporting**: Many affiliate networks report transactions with delays
- **Handles Updates**: Commission changes, status updates, and corrections are captured
- **Ensures Completeness**: No transactions are missed due to timing issues
- **Network Flexibility**: Different networks have different reporting schedules

## Logging Features

### API Call Logging
- Request/response details for all Strackr API calls
- Pagination progress and statistics
- Error handling with retry information
- Performance metrics and throughput

### Task Execution Logging
- Task start/completion with timing
- Progress updates during processing
- Data quality and validation statistics
- Detailed error reporting with context

### Database Operation Logging
- Supabase connection and initialization
- Batch upload progress and statistics
- Duplicate handling and conflict resolution
- Performance metrics and success rates

## Dependencies

- Strackr API credentials in environment variables or Secret Manager
- Supabase connection configured via environment variables
- Required packages: requests, pydantic, supabase-py

## Monitoring

- Email notifications on failures
- Detailed logging for each step with emojis for easy scanning
- Task-level error handling and recovery
- Performance metrics for optimization
- Data quality statistics for monitoring

## Smart Upsert Logic

The enhanced upload process provides:
- **Conflict Resolution**: Uses transaction_id as unique key to prevent duplicates
- **New vs Updated Tracking**: Monitors data freshness and delayed reporting
- **Batch Processing**: Efficient batch uploads with detailed progress tracking
- **Data Validation**: Non-blocking validation with monitoring alerts

## Debugging in Production

The enhanced logging provides:
- **API Call Tracing**: Every API request/response logged with timing
- **Data Flow Visibility**: Complete transaction flow with sample data
- **Upsert Statistics**: New vs updated transaction counts for monitoring
- **Data Freshness Metrics**: Update ratios and delayed reporting insights
- **Error Context**: Detailed error information with transaction IDs
- **Performance Metrics**: Timing and throughput for each step

Use log filters in Airflow UI to focus on specific log levels:
- `INFO`: General progress, statistics, and upsert details
- `WARNING`: Data quality issues, high update ratios, and non-fatal errors
- `ERROR`: Task failures and critical issues
- `DEBUG`: Detailed transaction-level information

## Monitoring and Alerts

Key metrics to monitor:
- **Update Ratio**: High ratios (>50%) indicate many delayed transactions
- **New Transaction Count**: Should be > 0 for healthy data flow
- **Failed Batches**: Should be 0 for successful runs
- **Data Freshness**: Tracks delayed reporting patterns by network
"""

logger.info("📚 DAG Documentation Updated")
logger.info("✅ Strackr Transactions DAG Initialization Complete")
logger.info("=" * 80)

if __name__ == "__main__":
    logger.info("🧪 Running DAG test...")
    dag.test()
