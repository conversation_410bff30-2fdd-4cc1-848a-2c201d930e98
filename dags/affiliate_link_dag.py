from airflow import DAG
from airflow.providers.google.cloud.operators.bigquery import BigQueryInsertJobOperator
from airflow.operators.python import PythonOperator
from datetime import datetime, timedelta
from google.cloud import bigquery
import logging
import os
from dependencies.utils.misc import IS_DEV
from dependencies.affiliate.common import get_bigquery_schema
import pandas as pd
from airflow.providers.google.cloud.operators.dataflow import DataflowStartFlexTemplateOperator
from airflow.providers.google.cloud.hooks.bigtable import BigtableHook

logger = logging.getLogger(__name__)

from dependencies.affiliate.shopmy_transform import load_shopmy_affiliates
from dependencies.affiliate.wildfire_transform import load_wildfire_affiliates
from dependencies.affiliate.sovrn_transform import load_sovrn_affiliates
from dependencies.affiliate.skimlinks_transform import load_skimlinks_affiliates

PROJECT_ID = 'phia-prod-416420'
DATASET_ID = 'affiliate'
SHOPMY_TABLE_ID = f'{PROJECT_ID}.{DATASET_ID}.advertiser_shopmy'
WILDFIRE_TABLE_ID = f'{PROJECT_ID}.{DATASET_ID}.advertiser_wildfire'
SOVRN_TABLE_ID = f'{PROJECT_ID}.{DATASET_ID}.advertiser_sovrn'
SKIMLINKS_TABLE_ID = f'{PROJECT_ID}.{DATASET_ID}.advertiser_skimlinks'
AFFILIATE_TABLE_ID = f'{PROJECT_ID}.{DATASET_ID}.merchant_links'

BIGTABLE_INSTANCE = 'phia-dict'
BIGTABLE_TABLE = 'affiliate-links'

def load_to_bigquery(df: pd.DataFrame, table_id: str):
    logger.info(f"Loading data to BigQuery table: {table_id}")
    client = bigquery.Client()
    
    try:
        table = client.get_table(table_id)
        current_schema = table.schema
        expected_schema = get_bigquery_schema()
        
        schema_matches = len(current_schema) == len(expected_schema)
        if schema_matches:
            for current_field, expected_field in zip(current_schema, expected_schema):
                if (current_field.name != expected_field.name or 
                    current_field.field_type != expected_field.field_type):
                    schema_matches = False
                    break
        
        if not schema_matches:
            logger.info(f"Schema mismatch detected for table {table_id}, dropping and recreating...")
            client.delete_table(table_id)
            table = bigquery.Table(table_id, schema=expected_schema)
            client.create_table(table)
        else:
            logger.info(f"Table {table_id} exists with matching schema, truncating...")
            truncate_query = f"TRUNCATE TABLE `{table_id}`"
            client.query(truncate_query).result()
    except Exception as e:
        logger.info(f"Table {table_id} does not exist or error occurred: {str(e)}, creating...")
        schema = get_bigquery_schema()
        table = bigquery.Table(table_id, schema=schema)
        client.create_table(table)
    
    job_config = bigquery.LoadJobConfig(
        write_disposition=bigquery.WriteDisposition.WRITE_APPEND,
        schema=get_bigquery_schema()
    )
    
    job = client.load_table_from_dataframe(df, table_id, job_config=job_config)
    job.result()
    logger.info(f"Loaded {len(df)} rows into {table_id}")

def load_shopmy_task(**kwargs):
    logger.info("Starting ShopMy task")
    df = load_shopmy_affiliates()
    load_to_bigquery(df, SHOPMY_TABLE_ID)
    logger.info("ShopMy task completed")

def load_wildfire_task(**kwargs):
    logger.info("Starting Wildfire task")
    df = load_wildfire_affiliates()
    load_to_bigquery(df, WILDFIRE_TABLE_ID)
    logger.info("Wildfire task completed")

def load_sovrn_task(**kwargs):
    logger.info("Starting Sovrn task")
    df = load_sovrn_affiliates()
    load_to_bigquery(df, SOVRN_TABLE_ID)
    logger.info("Sovrn task completed")

def load_skimlinks_task(**kwargs):
    logger.info("Starting Skimlinks task")
    df = load_skimlinks_affiliates()
    load_to_bigquery(df, SKIMLINKS_TABLE_ID)
    logger.info("Skimlinks task completed")

def clear_bigtable_prefix(prefix: str):
    """Clear rows in Bigtable that start with the given prefix."""
    try:
        bt_hook = BigtableHook()
        instance = bt_hook.get_instance(project_id=PROJECT_ID, instance_id=BIGTABLE_INSTANCE)
        table = instance.table(BIGTABLE_TABLE)

        table.drop_by_prefix(row_key_prefix=prefix.encode())
        logger.info(f"Successfully cleared rows with prefix {prefix} from Bigtable")
    except Exception as e:
        logger.error(f"Error clearing prefix {prefix} from Bigtable: {e}", exc_info=True)
        raise

def clear_staging_bigtable_task(**kwargs):
    logger.info("Clearing staging prefix in Bigtable")
    clear_bigtable_prefix("staging#")


def clear_prod_bigtable_task(**kwargs):
    logger.info("Clearing prod prefix in Bigtable")
    clear_bigtable_prefix("production#")

default_args = {
    'owner': 'airflow',
    'depends_on_past': False,
    'email_on_failure': True if not IS_DEV() else False,
    'email_on_retry': False,
    'retries': 3 if not IS_DEV() else 0,
    'retry_delay': timedelta(minutes=5),
    'email': '<EMAIL>',
}

with DAG(
    'affiliate_link_dag',
    default_args=default_args,
    description='Unified affiliate data pipeline for all networks',
    schedule='0 3 * * *' if not IS_DEV() else None,
    start_date=datetime(2024, 1, 1),
    catchup=False,
    tags=['affiliate', 'rates', 'bigquery'],
) as dag:

    load_shopmy = PythonOperator(
        task_id='load_shopmy',
        python_callable=load_shopmy_task,
    )

    load_wildfire = PythonOperator(
        task_id='load_wildfire',
        python_callable=load_wildfire_task,
    )

    load_sovrn = PythonOperator(
        task_id='load_sovrn',
        python_callable=load_sovrn_task,
    )

    load_skimlinks = PythonOperator(
        task_id='load_skimlinks',
        python_callable=load_skimlinks_task,
    )

    run_merge_query = BigQueryInsertJobOperator(
        task_id="run_merge_query",
        configuration={
            "query": {
                "query": "{% include 'dependencies/affiliate/merge_affiliate_tables.sql' %}",
                "useLegacySql": False,
            }
        },
        params={
            "affiliate_table_id": AFFILIATE_TABLE_ID,
            "shopmy_table_id": SHOPMY_TABLE_ID,
            "wildfire_table_id": WILDFIRE_TABLE_ID,
            "sovrn_table_id": SOVRN_TABLE_ID,
            "skimlinks_table_id": SKIMLINKS_TABLE_ID,
            'encoded_url': '{{encoded_url}}'
        }
    )

    clear_staging = PythonOperator(
        task_id='clear_staging_bigtable',
        python_callable=clear_staging_bigtable_task,
    )

    run_bq_to_bt_dataflow_staging = DataflowStartFlexTemplateOperator(
        task_id="run_bq_to_bt_dataflow_staging",
        project_id=PROJECT_ID,
        location="us-central1",
        body={
            "launchParameter": {
                "jobName": "affiliate-links-bq-to-bt-staging",
                "containerSpecGcsPath": "gs://dataflow-templates-us-central1/latest/flex/BigQuery_to_Bigtable",
                "parameters": {
                    "query": f"SELECT * REPLACE(CONCAT('staging#', row_key) AS row_key) FROM `{AFFILIATE_TABLE_ID}`",
                    "readIdColumn": "row_key",
                    "bigtableWriteInstanceId": BIGTABLE_INSTANCE,
                    "bigtableWriteTableId": BIGTABLE_TABLE,
                    "bigtableWriteColumnFamily": "values"
                }
            }
        },
        append_job_name=False,
        wait_until_finished=True,
    )

    run_bq_to_bt_dataflow_prod = DataflowStartFlexTemplateOperator(
        task_id="run_bq_to_bt_dataflow_prod",
        project_id=PROJECT_ID,
        location="us-central1",
        body={
            "launchParameter": {
                "jobName": "affiliate-links-bq-to-bt-prod",
                "containerSpecGcsPath": "gs://dataflow-templates-us-central1/latest/flex/BigQuery_to_Bigtable",
                "parameters": {
                    "query": f"SELECT * REPLACE(CONCAT('production#', row_key) AS row_key) FROM `{AFFILIATE_TABLE_ID}`",
                    "readIdColumn": "row_key",
                    "bigtableWriteInstanceId": BIGTABLE_INSTANCE,
                    "bigtableWriteTableId": BIGTABLE_TABLE,
                    "bigtableWriteColumnFamily": "values"
                }
            }
        },
        append_job_name=False,
        wait_until_finished=True,
    )

    clear_prod = PythonOperator(
        task_id='clear_prod_bigtable',
        python_callable=clear_prod_bigtable_task,
    )

    [load_shopmy, load_wildfire, load_sovrn, load_skimlinks] >> run_merge_query >> clear_staging >> run_bq_to_bt_dataflow_staging >> clear_prod >> run_bq_to_bt_dataflow_prod 