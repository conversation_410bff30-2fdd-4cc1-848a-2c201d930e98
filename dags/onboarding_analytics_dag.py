"""
Onboarding Analytics DAG

Daily analytics pipeline that processes Mixpanel onboarding and Safari permissions data in BigQuery
and pushes aggregated insights to Supabase for dashboard consumption.

Author: Data Team
Created: 2024-01-23
"""

from airflow import DAG
from airflow.operators.python import PythonOperator
from datetime import datetime, timedelta
from dependencies.onboarding.onboarding_tasks import (
    run_onboarding_completion_query,
    run_safari_permissions_query,
    push_onboarding_to_supabase
)

default_args = {
    'owner': 'data-team',
    'depends_on_past': False,
    'email_on_failure': True,
    'email_on_retry': False,
    'retries': 2,
    'retry_delay': timedelta(minutes=5),
    'email': ['<EMAIL>'],
}

# DAG definition
with DAG(
    dag_id="onboarding_analytics",
    default_args=default_args,
    description="Daily onboarding analytics pipeline using direct BigQuery execution",
    schedule="0 4 * * *",
    start_date=datetime(2024, 1, 23),
    catchup=False,
    tags=["bigquery", "analytics", "mixpanel", "supabase", "daily", "onboarding"],
    max_active_runs=1,
) as dag:
    
    # Task 1: Get onboarding completion metrics
    get_onboarding_completion = PythonOperator(
        task_id="get_onboarding_completion",
        python_callable=run_onboarding_completion_query,
    )
    
    # Task 2: Get Safari permissions funnel metrics
    get_safari_permissions = PythonOperator(
        task_id="get_safari_permissions",
        python_callable=run_safari_permissions_query,
    )
    
    # Task 3: Push all results to Supabase
    push_to_supabase = PythonOperator(
        task_id="push_to_supabase",
        python_callable=push_onboarding_to_supabase,
        trigger_rule='none_failed_or_skipped',
    )
    
    # Define dependencies - both queries can run in parallel, then push to Supabase
    [get_onboarding_completion, get_safari_permissions] >> push_to_supabase