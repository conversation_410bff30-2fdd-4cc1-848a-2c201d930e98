"""
Simplified Strackr DAG task functions.
Contains all the logic for fetching, transforming, and uploading Strackr transaction data.
"""

import os
import logging
import time
from datetime import datetime
from typing import Dict, Any, List
from supabase import create_client, Client
from google.cloud import secretmanager

from .strackr_client import (
    StrackrClient,
    get_yesterday_date_range,
    get_last_week_date_range,
)
from .models import StrackrTransaction, transform_strackr_data

logger = logging.getLogger(__name__)


def _get_credential(env_var: str, secret_name: str) -> str:
    """Get credential from environment variable or Secret Manager."""
    logger.info(f"Getting credential for {secret_name}...")

    # Try environment variable first
    value = os.getenv(env_var)
    if value:
        logger.info(f"✅ Retrieved credential {secret_name} from environment variable")
        return value

    # Try Secret Manager
    try:
        logger.info(
            f"🔍 Attempting to retrieve credential {secret_name} from Secret Manager"
        )
        start_time = time.time()

        client = secretmanager.SecretManagerServiceClient()
        project_id = "609155540540"
        secret_path = f"projects/{project_id}/secrets/{secret_name}/versions/latest"

        logger.info(f"Secret path: {secret_path}")
        response = client.access_secret_version(request={"name": secret_path})

        secret_time = time.time() - start_time
        logger.info(
            f"✅ Successfully retrieved credential {secret_name} from Secret Manager in {secret_time:.2f} seconds"
        )

        return response.payload.data.decode("UTF-8")

    except Exception as e:
        logger.error(
            f"❌ Failed to get credential {secret_name} from Secret Manager: {e}"
        )
        logger.error(f"Exception type: {type(e).__name__}")
        return ""


def fetch_strackr_transactions(**context) -> Dict[str, Any]:
    """
    Fetch transactions from Strackr API for the last 7 days.
    This ensures we capture any delayed or updated transactions.

    Returns:
        Dictionary with fetch results and transaction data
    """
    logger.info("=" * 60)
    logger.info("🚀 STARTING TASK: Fetch Strackr Transactions")
    logger.info("=" * 60)

    task_start_time = time.time()

    try:
        # Log context information
        logger.info("📋 Task Context Information:")
        logger.info(f"   • Task ID: {context.get('task_instance', {}).task_id}")
        logger.info(f"   • DAG ID: {context.get('dag', {}).dag_id}")
        logger.info(f"   • Execution Date: {context.get('execution_date')}")
        logger.info(f"   • Run ID: {context.get('run_id')}")

        # Get last 7 days date range
        logger.info("📅 Step 1: Getting date range...")
        step_start = time.time()
        start_date, end_date = get_last_week_date_range()
        step_time = time.time() - step_start
        logger.info(f"✅ Date range calculated in {step_time:.2f} seconds")
        logger.info(f"   • Start Date: {start_date}")
        logger.info(f"   • End Date: {end_date}")
        logger.info(
            f"   • Strategy: 7-day rolling window to capture delayed transactions"
        )

        # Initialize and fetch transactions
        logger.info(
            "🔗 Step 2: Initializing Strackr client and fetching transactions..."
        )
        step_start = time.time()
        client = StrackrClient()
        logger.info("✅ Strackr client initialized successfully")

        raw_transactions = client.get_transactions(start_date, end_date)
        step_time = time.time() - step_start
        logger.info(f"✅ Transactions fetched in {step_time:.2f} seconds")

        # TASK 1 SUMMARY LOG: Date range pulled
        logger.info("=" * 60)
        logger.info("📅 TASK 1 SUMMARY: Data Fetch Completed")
        logger.info(f"   • Date Range Pulled: {start_date} to {end_date}")
        logger.info(f"   • Total Days: 7 (rolling window)")
        logger.info(f"   • Transactions Retrieved: {len(raw_transactions)}")
        logger.info(f"   • API Source: Strackr Transactions API")
        logger.info(
            f"   • Strategy: 7-day window ensures delayed transactions are captured"
        )
        logger.info("=" * 60)

        # Prepare result
        result = {
            "success": True,
            "transaction_count": len(raw_transactions),
            "date_range": {"start": start_date, "end": end_date},
            "raw_transactions": raw_transactions,
            "fetch_time": step_time,
            "task_execution_time": time.time() - task_start_time,
        }

        # Log detailed statistics
        logger.info("📊 Fetch Results Summary:")
        logger.info(f"   • Total transactions: {len(raw_transactions)}")
        logger.info(f"   • Fetch time: {step_time:.2f} seconds")

        if raw_transactions:
            # Analyze transaction data
            currencies = set()
            merchants = set()
            statuses = set()
            total_amount = 0

            for tx in raw_transactions:
                currencies.add(tx.get("currency", "Unknown"))
                merchants.add(tx.get("advertiser_name", "Unknown"))
                statuses.add(tx.get("status_id", "Unknown"))
                total_amount += float(tx.get("order_amount", 0))

            logger.info(
                f"   • Unique currencies: {len(currencies)} - {list(currencies)}"
            )
            logger.info(f"   • Unique merchants: {len(merchants)}")
            logger.info(f"   • Unique statuses: {list(statuses)}")
            logger.info(f"   • Total order amount: ${total_amount:,.2f}")

            # Log sample transaction for verification
            sample_tx = raw_transactions[0]
            logger.info("📄 Sample Transaction:")
            logger.info(f"   • ID: {sample_tx.get('id')}")
            logger.info(f"   • Merchant: {sample_tx.get('advertiser_name')}")
            logger.info(f"   • Amount: ${sample_tx.get('order_amount', 0)}")
            logger.info(f"   • Currency: {sample_tx.get('currency')}")
            logger.info(f"   • Status: {sample_tx.get('status_id')}")
        else:
            logger.warning("⚠️  No transactions found for the specified date range")

        task_time = time.time() - task_start_time
        logger.info("=" * 60)
        logger.info(
            f"✅ TASK COMPLETED: Fetch Strackr Transactions in {task_time:.2f} seconds"
        )
        logger.info("=" * 60)

        return result

    except Exception as e:
        task_time = time.time() - task_start_time
        error_msg = f"Failed to fetch transactions: {e}"

        logger.error("=" * 60)
        logger.error(
            f"❌ TASK FAILED: Fetch Strackr Transactions after {task_time:.2f} seconds"
        )
        logger.error(f"❌ Error: {error_msg}")
        logger.error(f"❌ Exception Type: {type(e).__name__}")
        logger.error("=" * 60)

        return {
            "success": False,
            "error": str(e),
            "transaction_count": 0,
            "task_execution_time": task_time,
        }


def transform_strackr_transactions(**context) -> Dict[str, Any]:
    """
    Transform raw Strackr transactions to normalized format.

    Returns:
        Dictionary with transformation results
    """
    logger.info("=" * 60)
    logger.info("🔄 STARTING TASK: Transform Strackr Transactions")
    logger.info("=" * 60)

    task_start_time = time.time()

    try:
        # Log context information
        logger.info("📋 Task Context Information:")
        logger.info(f"   • Task ID: {context.get('task_instance', {}).task_id}")
        logger.info(f"   • Previous Task: fetch_strackr_transactions")

        # Get raw transactions from previous task
        logger.info("📥 Step 1: Retrieving raw transactions from previous task...")
        step_start = time.time()

        fetch_result = context["task_instance"].xcom_pull(
            task_ids="fetch_strackr_transactions"
        )

        step_time = time.time() - step_start
        logger.info(f"✅ XCom data retrieved in {step_time:.2f} seconds")

        if not fetch_result["success"]:
            error_msg = (
                f"Fetch task failed: {fetch_result.get('error', 'Unknown error')}"
            )
            logger.error(f"❌ {error_msg}")
            raise Exception(error_msg)

        raw_transactions = fetch_result["raw_transactions"]
        logger.info(f"📊 Retrieved {len(raw_transactions)} raw transactions")

        if not raw_transactions:
            logger.warning("⚠️  No raw transactions to transform")
            result = {
                "success": True,
                "total_raw": 0,
                "transformed_count": 0,
                "failed_count": 0,
                "transactions": [],
                "task_execution_time": time.time() - task_start_time,
            }
            logger.info("✅ Transform task completed (no data to process)")
            return result

        # Transform transactions
        logger.info("🔄 Step 2: Starting transaction transformation...")
        transform_start = time.time()

        transformed_transactions = []
        failed_count = 0
        failed_transactions = []

        logger.info(f"🔄 Processing {len(raw_transactions)} transactions...")

        # Process in batches for better logging
        batch_size = 100
        for batch_start in range(0, len(raw_transactions), batch_size):
            batch_end = min(batch_start + batch_size, len(raw_transactions))
            batch_raw = raw_transactions[batch_start:batch_end]

            logger.info(
                f"🔄 Processing batch {batch_start//batch_size + 1}: transactions {batch_start+1}-{batch_end}"
            )
            batch_start_time = time.time()

            batch_transformed = 0
            batch_failed = 0

            for i, raw_tx in enumerate(batch_raw):
                try:
                    transformed = transform_strackr_data(raw_tx)
                    if transformed:
                        transformed_transactions.append(transformed)
                        batch_transformed += 1
                    else:
                        batch_failed += 1
                        failed_count += 1
                        failed_transactions.append(
                            {
                                "id": raw_tx.get("id", "unknown"),
                                "error": "Transform function returned None",
                            }
                        )
                except Exception as e:
                    batch_failed += 1
                    failed_count += 1
                    failed_transactions.append(
                        {"id": raw_tx.get("id", "unknown"), "error": str(e)}
                    )
                    logger.info(
                        f"Failed to transform transaction {raw_tx.get('id')}: {e}"
                    )

            batch_time = time.time() - batch_start_time
            logger.info(
                f"✅ Batch {batch_start//batch_size + 1} completed in {batch_time:.2f}s: {batch_transformed} success, {batch_failed} failed"
            )

        transform_time = time.time() - transform_start

        # TASK 2 SUMMARY LOG: Transformation details
        logger.info("=" * 60)
        logger.info("🔄 TASK 2 SUMMARY: Data Transformation Completed")
        logger.info(
            f"   • Transformation Applied: Raw Strackr API data to normalized transaction format"
        )
        logger.info(f"   • Input Records: {len(raw_transactions)} raw transactions")
        logger.info(
            f"   • Output Records: {len(transformed_transactions)} normalized transactions"
        )
        logger.info(
            f"   • Transformation Logic: StrackrTransaction model validation and normalization"
        )
        logger.info(
            f"   • Success Rate: {(len(transformed_transactions)/len(raw_transactions)*100):.1f}%"
        )
        logger.info(f"   • Failed Records: {failed_count}")
        logger.info("=" * 60)

        # Log detailed transformation statistics
        logger.info("📊 Transformation Results:")
        logger.info(f"   • Total raw transactions: {len(raw_transactions)}")
        logger.info(f"   • Successfully transformed: {len(transformed_transactions)}")
        logger.info(f"   • Failed transformations: {failed_count}")
        logger.info(
            f"   • Success rate: {(len(transformed_transactions)/len(raw_transactions)*100):.1f}%"
        )
        logger.info(f"   • Transform time: {transform_time:.2f} seconds")
        logger.info(
            f"   • Throughput: {len(raw_transactions)/transform_time:.1f} transactions/second"
        )

        if failed_count > 0:
            logger.warning(f"⚠️  {failed_count} transactions failed transformation")
            # Log sample failures for debugging
            sample_failures = failed_transactions[:5]
            for failure in sample_failures:
                logger.warning(f"   • Failed TX {failure['id']}: {failure['error']}")
            if len(failed_transactions) > 5:
                logger.warning(
                    f"   • ... and {len(failed_transactions) - 5} more failures"
                )

        # Analyze transformed data
        if transformed_transactions:
            logger.info("📊 Transformed Data Analysis:")
            currencies = set()
            merchants = set()
            statuses = set()

            for tx in transformed_transactions:
                currencies.add(tx.currency)
                merchants.add(tx.merchant_name)
                statuses.add(tx.status)

            logger.info(f"   • Unique currencies: {list(currencies)}")
            logger.info(f"   • Unique merchants: {len(merchants)}")
            logger.info(f"   • Unique statuses: {list(statuses)}")

            # Log sample transformed transaction
            sample_tx = transformed_transactions[0]
            logger.info("📄 Sample Transformed Transaction:")
            logger.info(f"   • Transaction ID: {sample_tx.transaction_id}")
            logger.info(f"   • Merchant: {sample_tx.merchant_name}")
            logger.info(f"   • Amount: ${sample_tx.order_amount}")
            logger.info(f"   • Status: {sample_tx.status}")

        result = {
            "success": True,
            "total_raw": len(raw_transactions),
            "transformed_count": len(transformed_transactions),
            "failed_count": failed_count,
            "transactions": [tx.to_dict() for tx in transformed_transactions],
            "failed_transactions": failed_transactions[
                :10
            ],  # Store first 10 failures for debugging
            "transform_time": transform_time,
            "task_execution_time": time.time() - task_start_time,
        }

        task_time = time.time() - task_start_time
        logger.info("=" * 60)
        logger.info(
            f"✅ TASK COMPLETED: Transform Strackr Transactions in {task_time:.2f} seconds"
        )
        logger.info("=" * 60)

        return result

    except Exception as e:
        task_time = time.time() - task_start_time
        error_msg = f"Failed to transform transactions: {e}"

        logger.error("=" * 60)
        logger.error(
            f"❌ TASK FAILED: Transform Strackr Transactions after {task_time:.2f} seconds"
        )
        logger.error(f"❌ Error: {error_msg}")
        logger.error(f"❌ Exception Type: {type(e).__name__}")
        logger.error("=" * 60)

        return {
            "success": False,
            "error": str(e),
            "transformed_count": 0,
            "task_execution_time": task_time,
        }


def upload_to_supabase(**context) -> Dict[str, Any]:
    """
    Upload transformed transactions to Supabase with smart upsert logic.

    This function:
    1. Fetches existing transactions for the date range from Supabase
    2. Uses upsert to handle both new and updated transactions
    3. Provides detailed statistics about new vs updated records
    4. Ensures data consistency across the 7-day rolling window

    Returns:
        Dictionary with upload results including upsert statistics
    """
    logger.info("=" * 60)
    logger.info("📤 STARTING TASK: Upload to Supabase")
    logger.info("=" * 60)

    task_start_time = time.time()

    try:
        # Log context information
        logger.info("📋 Task Context Information:")
        logger.info(f"   • Task ID: {context.get('task_instance', {}).task_id}")
        logger.info(f"   • Previous Task: transform_strackr_transactions")

        # Get transformed transactions from previous task
        logger.info(
            "📥 Step 1: Retrieving transformed transactions from previous task..."
        )
        step_start = time.time()

        transform_result = context["task_instance"].xcom_pull(
            task_ids="transform_strackr_transactions"
        )

        step_time = time.time() - step_start
        logger.info(f"✅ XCom data retrieved in {step_time:.2f} seconds")

        if not transform_result["success"]:
            error_msg = f"Transform task failed: {transform_result.get('error', 'Unknown error')}"
            logger.error(f"❌ {error_msg}")
            raise Exception(error_msg)

        transactions = transform_result["transactions"]
        logger.info(f"📊 Retrieved {len(transactions)} transformed transactions")

        if not transactions:
            logger.info("ℹ️  No transactions to upload")
            result = {
                "success": True,
                "uploaded_count": 0,
                "new_count": 0,
                "updated_count": 0,
                "message": "No transactions to upload",
                "task_execution_time": time.time() - task_start_time,
            }

            task_time = time.time() - task_start_time
            logger.info("=" * 60)
            logger.info(
                f"✅ TASK COMPLETED: Upload to Supabase in {task_time:.2f} seconds (no data)"
            )
            logger.info("=" * 60)
            return result

        # Initialize Supabase client
        logger.info("🔗 Step 2: Initializing Supabase client...")
        step_start = time.time()

        logger.info("Retrieving Supabase credentials...")
        supabase_url = _get_credential("SUPABASE_URL", "SUPABASE_URL")
        supabase_key = _get_credential(
            "SUPABASE_SERVICE_ROLE_KEY", "SUPABASE_SERVICE_ROLE_KEY"
        )

        # Mask sensitive data in logs
        masked_url = (
            f"{supabase_url[:50]}..."
            if supabase_url and len(supabase_url) > 50
            else (supabase_url if supabase_url else "Not set")
        )
        logger.info(f"   • Supabase URL: {masked_url}")
        logger.info(
            f"   • Service Role Key: {'*' * len(supabase_key) if supabase_key else 'Not set'}"
        )

        logger.info(
            f"   • Supabase URL: {supabase_url[:50]}..."
            if supabase_url
            else "   • Supabase URL: Not set"
        )
        logger.info(
            f"   • Service Role Key: {'*' * len(supabase_key) if supabase_key else 'Not set'}"
        )

        if not supabase_url or not supabase_key:

            error_msg = "SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY credentials not found in environment variables or Secret Manager"

            logger.error(f"❌ {error_msg}")
            raise ValueError(error_msg)

        supabase: Client = create_client(supabase_url, supabase_key)
        step_time = time.time() - step_start
        logger.info(f"✅ Supabase client initialized in {step_time:.2f} seconds")

        # Step 3: Fetch existing transactions for comparison (optional for logging)
        logger.info("📊 Step 3: Fetching existing transactions for comparison...")
        step_start = time.time()

        # Get date range from the first transaction to understand the scope
        if transactions:
            # Extract date range from transactions for logging
            transaction_dates = [
                tx.get("transaction_date", "")[:10]
                for tx in transactions
                if tx.get("transaction_date")
            ]
            unique_dates = sorted(set(transaction_dates))
            logger.info(
                f"   • New transactions span dates: {unique_dates[0]} to {unique_dates[-1]}"
                if unique_dates
                else "   • No valid dates found"
            )
            logger.info(f"   • Total unique dates: {len(unique_dates)}")

        step_time = time.time() - step_start
        logger.info(f"✅ Existing data analysis completed in {step_time:.2f} seconds")

        # Upload in batches with smart upsert
        logger.info("📤 Step 4: Starting batch upload with smart upsert...")
        batch_size = 100
        total_uploaded = 0
        total_new = 0
        total_updated = 0
        errors = []
        upload_start = time.time()

        logger.info(f"📊 Upload Configuration:")
        logger.info(f"   • Total transactions: {len(transactions)}")
        logger.info(f"   • Batch size: {batch_size}")
        logger.info(
            f"   • Total batches: {(len(transactions) + batch_size - 1) // batch_size}"
        )
        logger.info(f"   • Target table: normalized_transactions")
        logger.info(f"   • Upsert strategy: Conflict resolution on transaction_id")
        logger.info(f"   • 7-day window: Ensures delayed transactions are captured")

        for i in range(0, len(transactions), batch_size):
            batch_num = i // batch_size + 1
            batch = transactions[i : i + batch_size]

            logger.info(
                f"📤 Processing batch {batch_num}: transactions {i+1}-{min(i+batch_size, len(transactions))}"
            )
            batch_start_time = time.time()

            try:
                # Check existing transactions in this batch for new vs update tracking
                batch_transaction_ids = [tx.get("transaction_id") for tx in batch]

                # Query existing transactions for this batch
                existing_check = (
                    supabase.table("normalized_transactions")
                    .select("transaction_id")
                    .in_("transaction_id", batch_transaction_ids)
                    .execute()
                )

                existing_ids = (
                    set(tx["transaction_id"] for tx in existing_check.data)
                    if existing_check.data
                    else set()
                )
                batch_new_count = len(
                    [
                        tx_id
                        for tx_id in batch_transaction_ids
                        if tx_id not in existing_ids
                    ]
                )
                batch_update_count = len(batch) - batch_new_count

                logger.info(
                    f"   • Batch {batch_num} analysis: {batch_new_count} new, {batch_update_count} updates"
                )

                # Use upsert to handle both new and updated transactions
                logger.info(f"Executing smart upsert for batch {batch_num}...")
                result = (
                    supabase.table("normalized_transactions")
                    .upsert(batch, on_conflict="transaction_id")
                    .execute()
                )

                batch_uploaded = len(result.data) if result.data else len(batch)
                total_uploaded += batch_uploaded
                total_new += batch_new_count
                total_updated += batch_update_count

                batch_time = time.time() - batch_start_time
                logger.info(
                    f"✅ Batch {batch_num} completed in {batch_time:.2f}s: {batch_uploaded} transactions processed"
                )
                logger.info(
                    f"   • New: {batch_new_count}, Updated: {batch_update_count}"
                )

                if result.data:
                    logger.info(
                        f"Sample processed transaction IDs: {[tx.get('transaction_id', 'no-id') for tx in result.data[:3]]}"
                    )

            except Exception as e:
                batch_time = time.time() - batch_start_time
                error_msg = (
                    f"Batch {batch_num} failed after {batch_time:.2f}s: {str(e)}"
                )
                logger.error(f"❌ {error_msg}")
                logger.error(f"Exception type: {type(e).__name__}")
                errors.append(error_msg)

                # Log sample transaction data for debugging
                if batch:
                    sample_tx = batch[0]
                    logger.info(f"Sample transaction from failed batch: {sample_tx}")

        upload_time = time.time() - upload_start

        # TASK 3 SUMMARY LOG: Upload details
        logger.info("=" * 60)
        logger.info("📤 TASK 3 SUMMARY: Data Upload Completed")
        logger.info(f"   • Destination: Supabase normalized_transactions table")
        logger.info(f"   • Records Processed: {total_uploaded} transactions")
        logger.info(f"   • New Records: {total_new} transactions")
        logger.info(f"   • Updated Records: {total_updated} transactions")
        logger.info(f"   • Input Records: {len(transactions)} normalized transactions")
        logger.info(f"   • Upload Method: Smart upsert with 7-day rolling window")
        logger.info(f"   • Conflict Resolution: transaction_id (ensures no duplicates)")
        logger.info(f"   • Batch Size: {batch_size} records per batch")
        logger.info(f"   • Success Rate: {(total_uploaded/len(transactions)*100):.1f}%")
        logger.info(f"   • Failed Batches: {len(errors)}")
        logger.info("=" * 60)

        # Log final upload statistics
        logger.info("📊 Upload Results Summary:")
        logger.info(f"   • Total transactions processed: {len(transactions)}")
        logger.info(f"   • Successfully uploaded: {total_uploaded}")
        logger.info(f"   • New transactions: {total_new}")
        logger.info(f"   • Updated transactions: {total_updated}")
        logger.info(f"   • Failed batches: {len(errors)}")
        logger.info(f"   • Success rate: {(total_uploaded/len(transactions)*100):.1f}%")
        logger.info(f"   • Upload time: {upload_time:.2f} seconds")
        logger.info(
            f"   • Throughput: {total_uploaded/upload_time:.1f} transactions/second"
        )

        # Data freshness validation (non-blocking)
        logger.info("🔍 Data Freshness Validation:")
        if total_new > 0:
            logger.info(
                f"   ✅ {total_new} new transactions captured (good data freshness)"
            )
        if total_updated > 0:
            logger.info(
                f"   🔄 {total_updated} transactions updated (delayed reporting captured)"
            )
        if total_new == 0 and total_updated == 0:
            logger.warning(
                f"   ⚠️  No new or updated transactions - this may indicate an issue"
            )

        # Calculate update ratio for monitoring
        update_ratio = (
            (total_updated / total_uploaded * 100) if total_uploaded > 0 else 0
        )
        logger.info(
            f"   📊 Update ratio: {update_ratio:.1f}% (indicates delayed transaction reporting)"
        )

        if update_ratio > 50:
            logger.warning(
                f"   ⚠️  High update ratio ({update_ratio:.1f}%) - many delayed transactions"
            )
        elif update_ratio > 20:
            logger.info(
                f"   ℹ️  Moderate update ratio ({update_ratio:.1f}%) - normal delayed reporting"
            )
        else:
            logger.info(
                f"   ✅ Low update ratio ({update_ratio:.1f}%) - mostly fresh data"
            )

        if errors:
            logger.warning(f"⚠️  {len(errors)} batches failed during upload:")
            for error in errors:
                logger.warning(f"   • {error}")

        result = {
            "success": len(errors) == 0,
            "total_transactions": len(transactions),
            "uploaded_count": total_uploaded,
            "new_count": total_new,
            "updated_count": total_updated,
            "error_count": len(errors),
            "errors": errors,
            "upload_time": upload_time,
            "task_execution_time": time.time() - task_start_time,
        }

        task_time = time.time() - task_start_time

        if result["success"]:
            logger.info("=" * 60)
            logger.info(
                f"✅ TASK COMPLETED: Upload to Supabase in {task_time:.2f} seconds"
            )
            logger.info(f"✅ All {total_uploaded} transactions processed successfully")
            logger.info(f"✅ New: {total_new}, Updated: {total_updated}")
            logger.info("=" * 60)
        else:
            logger.warning("=" * 60)
            logger.warning(
                f"⚠️  TASK COMPLETED WITH ERRORS: Upload to Supabase in {task_time:.2f} seconds"
            )
            logger.warning(
                f"⚠️  {total_uploaded}/{len(transactions)} transactions processed"
            )
            logger.warning(f"⚠️  New: {total_new}, Updated: {total_updated}")
            logger.warning("=" * 60)

        return result

    except Exception as e:
        task_time = time.time() - task_start_time
        error_msg = f"Failed to upload to Supabase: {e}"

        logger.error("=" * 60)
        logger.error(
            f"❌ TASK FAILED: Upload to Supabase after {task_time:.2f} seconds"
        )
        logger.error(f"❌ Error: {error_msg}")
        logger.error(f"❌ Exception Type: {type(e).__name__}")
        logger.error("=" * 60)

        return {
            "success": False,
            "error": str(e),
            "uploaded_count": 0,
            "task_execution_time": task_time,
        }
