"""
Simple Strackr API client for transaction data.
Handles authentication, API requests, and basic error handling.
"""

import os
import requests
import logging
import time
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from google.cloud import secretmanager

logger = logging.getLogger(__name__)


class StrackrClient:
    """Simple client for Strackr transactions API."""

    def __init__(self):
        """Initialize the client with credentials from environment or Secret Manager."""
        logger.info("=== Initializing Strackr Client ===")
        start_time = time.time()

        self.base_url = "https://api.strackr.com/v4"
        self.session = requests.Session()
        self.session.timeout = 30

        logger.info(f"Base URL set to: {self.base_url}")
        logger.info(f"Session timeout set to: {self.session.timeout} seconds")

        # Get credentials
        logger.info("Retrieving Strackr API credentials...")

        self.api_id = self._get_credential("STRACKR_API_ID", "STRACKR_API_ID")
        self.api_key = self._get_credential("STRACKR_API_KEY", "STRACKR_API_KEY")

        if not self.api_id or not self.api_key:

            error_msg = "Strackr API credentials not found"
            logger.error(f"❌ {error_msg}")
            raise ValueError(error_msg)

        # Mask sensitive data in logs
        masked_api_id = (
            f"{self.api_id[:4]}...{self.api_id[-4:]}" if len(self.api_id) > 8 else "***"
        )
        logger.info(f"✅ API ID retrieved: {masked_api_id}")
        logger.info(f"✅ API Key retrieved: {'*' * len(self.api_key)}")

        initialization_time = time.time() - start_time
        logger.info(
            f"✅ Strackr client initialized successfully in {initialization_time:.2f} seconds"
        )

    def _get_credential(self, env_var: str, secret_name: str) -> str:
        """Get credential from environment variable or Secret Manager."""
        logger.info(f"Getting credential for {secret_name}...")

        # Try environment variable first
        value = os.getenv(env_var)
        if value:

            logger.info(
                f"✅ Retrieved credential {secret_name} from environment variable"
            )

            return value

        # Try Secret Manager
        try:

            logger.info(
                f"🔍 Attempting to retrieve credential {secret_name} from Secret Manager"
            )
            start_time = time.time()

            client = secretmanager.SecretManagerServiceClient()
            project_id = "609155540540"
            secret_path = f"projects/{project_id}/secrets/{secret_name}/versions/latest"

            logger.info(f"Secret path: {secret_path}")
            response = client.access_secret_version(request={"name": secret_path})

            secret_time = time.time() - start_time
            logger.info(
                f"✅ Successfully retrieved credential {secret_name} from Secret Manager in {secret_time:.2f} seconds"
            )

            return response.payload.data.decode("UTF-8")

        except Exception as e:

            logger.error(
                f"❌ Failed to get credential {secret_name} from Secret Manager: {e}"
            )
            logger.error(f"Exception type: {type(e).__name__}")

            return ""

    def get_transactions(
        self, start_date: str, end_date: str, currency: str = "USD"
    ) -> List[Dict[str, Any]]:
        """
        Fetch transactions from Strackr API.

        Args:
            start_date: Start date in YYYY-MM-DD format
            end_date: End date in YYYY-MM-DD format
            currency: Currency filter (default: USD)

        Returns:
            List of transaction dictionaries
        """

        logger.info("=== Starting Strackr Transactions Fetch ===")
        logger.info(f"📅 Date range: {start_date} to {end_date}")
        logger.info(f"💰 Currency filter: {currency}")

        fetch_start_time = time.time()

        url = f"{self.base_url}/ad/reports/transactions"
        logger.info(f"🌐 API Endpoint: {url}")

        params = {
            "api_id": self.api_id,
            "api_key": self.api_key,
            "time_start": start_date,
            "time_end": end_date,
            "time_type": "sold_at",
            "currency": currency,
            "limit": 100,
            "page": 1,
            "expand": [
                "reason",
                "network_favicon",
                "advertiser_favicon",
                "basket",
                "event",
            ],
        }

        # Log request parameters (masking sensitive data)
        safe_params = params.copy()
        safe_params["api_id"] = (
            f"{params['api_id'][:4]}...{params['api_id'][-4:]}"
            if len(params["api_id"]) > 8
            else "***"
        )
        safe_params["api_key"] = "*" * len(params["api_key"])
        logger.info(f"📋 Request parameters: {safe_params}")

        all_transactions = []
        page = 1
        total_api_calls = 0
        total_request_time = 0

        logger.info("🚀 Starting pagination loop...")

        while True:
            params["page"] = page
            page_start_time = time.time()

            logger.info(f"📄 Fetching page {page}...")

            try:
                # Make API request
                logger.info(f"Making GET request to {url} with page {page}")
                response = self.session.get(url, params=params)
                total_api_calls += 1

                request_time = time.time() - page_start_time
                total_request_time += request_time

                logger.info(
                    f"⏱️  Page {page} request completed in {request_time:.2f} seconds"
                )
                logger.info(f"📊 Response status: {response.status_code}")
                logger.info(f"📏 Response size: {len(response.content)} bytes")

                # Log response headers for debugging
                logger.info(f"Response headers: {dict(response.headers)}")

                response.raise_for_status()
                data = response.json()

                logger.info(
                    f"JSON response keys: {list(data.keys()) if isinstance(data, dict) else 'Not a dict'}"
                )

                transactions = data.get("data", [])
                logger.info(f"💼 Page {page} returned {len(transactions)} transactions")

                if not transactions:
                    logger.info(
                        f"🏁 No transactions found on page {page}, ending pagination"
                    )
                    break

                all_transactions.extend(transactions)

                logger.info(f"📈 Running total: {len(all_transactions)} transactions")

                # Log sample transaction IDs for debugging
                if transactions:
                    sample_ids = [tx.get("id", "no-id") for tx in transactions[:3]]
                    logger.info(
                        f"Sample transaction IDs from page {page}: {sample_ids}"
                    )

                # Check if there are more pages
                if len(transactions) < params["limit"]:
                    logger.info(
                        f"🏁 Page {page} returned fewer transactions than limit ({len(transactions)} < {params['limit']}), ending pagination"
                    )
                    break

                page += 1

                # Add a small delay between requests to be API-friendly
                if page > 1:
                    time.sleep(0.1)

            except requests.exceptions.RequestException as e:
                request_time = time.time() - page_start_time
                logger.error(
                    f"❌ API request failed on page {page} after {request_time:.2f} seconds: {e}"
                )
                logger.error(f"Exception type: {type(e).__name__}")

                if hasattr(e, "response") and e.response is not None:
                    logger.error(f"Response status code: {e.response.status_code}")
                    logger.error(f"Response text: {e.response.text[:500]}...")

                if page == 1:  # If first page fails, raise error
                    logger.error("🚨 First page failed, raising exception")
                    raise
                else:
                    logger.warning(
                        f"⚠️  Page {page} failed, returning {len(all_transactions)} transactions collected so far"
                    )
                    break  # If later page fails, return what we have

            except Exception as e:
                request_time = time.time() - page_start_time
                logger.error(
                    f"❌ Unexpected error on page {page} after {request_time:.2f} seconds: {e}"
                )
                logger.error(f"Exception type: {type(e).__name__}")
                if page == 1:
                    raise

                break

        # Log final statistics
        total_fetch_time = time.time() - fetch_start_time
        avg_request_time = (
            total_request_time / total_api_calls if total_api_calls > 0 else 0
        )

        logger.info("=== Strackr Fetch Summary ===")
        logger.info(f"✅ Total transactions fetched: {len(all_transactions)}")
        logger.info(f"📄 Total pages processed: {page}")
        logger.info(f"🌐 Total API calls made: {total_api_calls}")
        logger.info(f"⏱️  Total fetch time: {total_fetch_time:.2f} seconds")
        logger.info(f"⏱️  Total request time: {total_request_time:.2f} seconds")
        logger.info(f"⏱️  Average request time: {avg_request_time:.2f} seconds")
        logger.info(
            f"📊 Throughput: {len(all_transactions) / total_fetch_time:.2f} transactions/second"
        )

        if all_transactions:
            # Log some sample data for verification
            sample_transaction = all_transactions[0]
            logger.info(
                f"📋 Sample transaction fields: {list(sample_transaction.keys())}"
            )
            logger.info(f"Sample transaction data: {sample_transaction}")

        return all_transactions


def get_yesterday_date_range() -> tuple[str, str]:
    """Get yesterday's date range in YYYY-MM-DD format."""
    logger.info("📅 Calculating yesterday's date range...")

    yesterday = datetime.now() - timedelta(days=1)
    date_str = yesterday.strftime("%Y-%m-%d")

    logger.info(f"✅ Yesterday's date: {date_str}")
    return date_str, date_str


def get_last_week_date_range() -> tuple[str, str]:
    """
    Get last 7 days date range ending yesterday in YYYY-MM-DD format.
    This ensures we capture any delayed or updated transactions.
    """
    logger.info("📅 Calculating last 7 days date range...")

    end_date = datetime.now() - timedelta(days=1)  # Yesterday
    start_date = end_date - timedelta(days=6)  # 7 days total (including yesterday)

    start_str = start_date.strftime("%Y-%m-%d")
    end_str = end_date.strftime("%Y-%m-%d")

    logger.info(f"✅ Last 7 days range: {start_str} to {end_str}")
    logger.info(f"   • Start Date: {start_str} ({start_date.strftime('%A')})")
    logger.info(f"   • End Date: {end_str} ({end_date.strftime('%A')})")
    logger.info(f"   • Total Days: 7")

    return start_str, end_str
