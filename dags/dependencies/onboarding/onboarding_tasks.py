"""
Onboarding Analytics DAG Task Functions

This module contains all the task functions for the Onboarding Analytics DAG.
All BigQuery queries are executed directly without using BigQueryInsertJobOperator
to avoid job ID location mismatch issues.
"""

import os
import time
import logging
from datetime import datetime
from google.cloud import bigquery
from google.cloud import secretmanager
from supabase import create_client, Client

logger = logging.getLogger(__name__)

# Configuration
PROJECT_ID = 'phia-prod-416420'
MIXPANEL_DATASET = 'mixpanel'

def _get_credential(env_var: str, secret_name: str) -> str:
    """Get credential from environment variable or Secret Manager."""
    logger.info(f"Getting credential for {secret_name}...")

    # Try environment variable first
    value = os.getenv(env_var)
    if value:
        logger.info(f"✅ Retrieved credential {secret_name} from environment variable")
        return value

    # Try Secret Manager
    try:
        logger.info(f"🔍 Attempting to retrieve credential {secret_name} from Secret Manager")
        start_time = time.time()

        client = secretmanager.SecretManagerServiceClient()
        project_id = "609155540540"  # Same as apple tasks
        secret_path = f"projects/{project_id}/secrets/{secret_name}/versions/latest"

        logger.info(f"Secret path: {secret_path}")
        response = client.access_secret_version(request={"name": secret_path})

        secret_time = time.time() - start_time
        logger.info(f"✅ Successfully retrieved credential {secret_name} from Secret Manager in {secret_time:.2f} seconds")

        return response.payload.data.decode("UTF-8")

    except Exception as e:
        logger.error(f"❌ Failed to get credential {secret_name} from Secret Manager: {e}")
        logger.error(f"Exception type: {type(e).__name__}")
        raise

def run_onboarding_completion_query(**context):
    """Run onboarding completion with notifications query and return results"""
    logger.info("=== RUN_ONBOARDING_COMPLETION_QUERY START ===")
    ds = context['ds']
    
    query = f"""
    WITH
      -- Metric A: First time ever app_first_run per user
      app_first_run_users AS (
        SELECT
          phia_id,
          MIN(time) AS first_app_first_run_time
        FROM `{PROJECT_ID}.{MIXPANEL_DATASET}.app_first_run`
        WHERE phia_id IS NOT NULL
        GROUP BY phia_id
      ),

      -- Metric B: First time ever page_view per user with filters
      page_view_users AS (
        SELECT
          phia_id,
          MIN(time) AS first_page_view_time
        FROM `{PROJECT_ID}.{MIXPANEL_DATASET}.page_view`
        WHERE
          phia_id IS NOT NULL
          AND domain = 'phia.com'
          AND pathname = '/mobile/almost-finished'
        GROUP BY phia_id
      )

    SELECT
      (SELECT COUNT(*) FROM app_first_run_users) AS app_first_run_users,
      (SELECT COUNT(*) FROM page_view_users) AS page_view_users,
      SAFE_DIVIDE(
        (SELECT COUNT(*) FROM page_view_users),
        NULLIF((SELECT COUNT(*) FROM app_first_run_users), 0)
      ) * 100 AS percent_onboarded_with_notifications
    """
    
    # Helper function to convert pandas timestamps and handle NaN values
    def convert_timestamps(data):
        """Recursively convert pandas timestamps to ISO format strings and handle NaN values"""
        import pandas as pd
        import numpy as np
        from datetime import datetime, date
        
        if isinstance(data, list):
            return [convert_timestamps(item) for item in data]
        elif isinstance(data, dict):
            return {key: convert_timestamps(value) for key, value in data.items()}
        elif isinstance(data, pd.Timestamp):
            return data.isoformat()
        elif isinstance(data, (datetime, date)):
            return data.isoformat()
        elif hasattr(data, 'isoformat'):
            return data.isoformat()
        elif pd.isna(data) or (isinstance(data, float) and (np.isnan(data) or np.isinf(data))):
            return None  # Convert NaN/infinity to null for JSON
        else:
            return data

    try:
        client = bigquery.Client(project=PROJECT_ID)
        logger.info("Executing onboarding completion query...")
        query_job = client.query(query)
        df = query_job.to_dataframe()
        records = df.to_dict('records')
        logger.info(f"Query returned {len(records)} onboarding completion records")
        # Convert timestamps and NaN values before returning
        converted_records = convert_timestamps(records)
        return converted_records

    except Exception as e:
        logger.error(f"Onboarding completion query failed: {e}")
        raise

def run_safari_permissions_query(**context):
    """Run safari permissions funnel query and return results"""
    logger.info("=== RUN_SAFARI_PERMISSIONS_QUERY START ===")
    ds = context['ds']
    
    query = f"""
    WITH
      enable_phia_first_view AS (
        SELECT
          phia_id,
          MIN(time) AS enable_phia_time
        FROM `{PROJECT_ID}.{MIXPANEL_DATASET}.page_view`
        WHERE
          domain = 'phia.com'
          AND pathname = '/mobile/enable-phia'
          AND phia_id IS NOT NULL
        GROUP BY phia_id
      ),
      almost_finished_first_view AS (
        SELECT
          phia_id,
          MIN(time) AS almost_finished_time
        FROM `{PROJECT_ID}.{MIXPANEL_DATASET}.page_view`
        WHERE
          domain = 'phia.com'
          AND pathname = '/mobile/almost-finished'
          AND phia_id IS NOT NULL
        GROUP BY phia_id
      ),
      funnel_users AS (
        SELECT
          e.phia_id,
          e.enable_phia_time,
          a.almost_finished_time
        FROM enable_phia_first_view e
        JOIN almost_finished_first_view a
          ON e.phia_id = a.phia_id
          AND a.almost_finished_time >= e.enable_phia_time
          AND a.almost_finished_time <= TIMESTAMP_ADD(e.enable_phia_time, INTERVAL 1 DAY)
      )
    SELECT
      (SELECT COUNT(*) FROM enable_phia_first_view) AS step1_users,
      (SELECT COUNT(*) FROM funnel_users) AS converted_users,
      SAFE_DIVIDE(
        (SELECT COUNT(*) FROM funnel_users),
        (SELECT COUNT(*) FROM enable_phia_first_view)
      ) * 100 AS percent_converted
    """
    
    # Helper function to convert pandas timestamps and handle NaN values
    def convert_timestamps(data):
        """Recursively convert pandas timestamps to ISO format strings and handle NaN values"""
        import pandas as pd
        import numpy as np
        from datetime import datetime, date
        
        if isinstance(data, list):
            return [convert_timestamps(item) for item in data]
        elif isinstance(data, dict):
            return {key: convert_timestamps(value) for key, value in data.items()}
        elif isinstance(data, pd.Timestamp):
            return data.isoformat()
        elif isinstance(data, (datetime, date)):
            return data.isoformat()
        elif hasattr(data, 'isoformat'):
            return data.isoformat()
        elif pd.isna(data) or (isinstance(data, float) and (np.isnan(data) or np.isinf(data))):
            return None  # Convert NaN/infinity to null for JSON
        else:
            return data

    try:
        client = bigquery.Client(project=PROJECT_ID)
        logger.info("Executing safari permissions query...")
        query_job = client.query(query)
        df = query_job.to_dataframe()
        records = df.to_dict('records')
        logger.info(f"Query returned {len(records)} safari permissions records")
        # Convert timestamps and NaN values before returning
        converted_records = convert_timestamps(records)
        return converted_records
    except Exception as e:
        logger.error(f"Safari permissions query failed: {e}")
        raise

def push_onboarding_to_supabase(**context):
    """Combine all query results and push to Supabase"""
    logger.info(f"=== PUSH_ONBOARDING_TO_SUPABASE START ===")
    logger.info(f"Execution date: {context.get('ds')}")
    
    try:
        # Get Supabase credentials using the same pattern as apple_tasks
        logger.info("🔑 Fetching Supabase credentials...")
        supabase_url = _get_credential("SUPABASE_URL", "SUPABASE_URL")
        supabase_key = _get_credential("SUPABASE_SERVICE_ROLE_KEY", "SUPABASE_SERVICE_ROLE_KEY")
        
        if not supabase_url or not supabase_key:
            logger.error("❌ Supabase credentials are missing")
            logger.error(f"   • Supabase URL present: {bool(supabase_url)}")
            logger.error(f"   • Supabase Key present: {bool(supabase_key)}")
            raise ValueError("Supabase credentials are required")
        
        logger.info(f"✅ Supabase URL retrieved: {supabase_url[:50]}...")
        logger.info(f"✅ Supabase Key retrieved: {'*' * len(supabase_key) if supabase_key else 'None'}")
        logger.info("🔧 Creating Supabase client...")
        supabase: Client = create_client(supabase_url, supabase_key)
        logger.info("✅ Supabase client created successfully")
        
        # Get results from upstream tasks via XCom
        logger.info("Pulling data from upstream tasks...")
        
        logger.info("Pulling onboarding_completion from get_onboarding_completion...")
        onboarding_completion = context['task_instance'].xcom_pull(task_ids='get_onboarding_completion')
        logger.info(f"Onboarding completion: {len(onboarding_completion) if onboarding_completion else 0} records")
        
        logger.info("Pulling safari_permissions from get_safari_permissions...")
        safari_permissions = context['task_instance'].xcom_pull(task_ids='get_safari_permissions')
        logger.info(f"Safari permissions: {len(safari_permissions) if safari_permissions else 0} records")
        
        execution_date = context['ds']
        logger.info(f"Using execution date: {execution_date}")
        
        # Helper function to convert pandas timestamps and handle NaN values
        def convert_timestamps(data):
            """Recursively convert pandas timestamps to ISO format strings and handle NaN values"""
            import pandas as pd
            import numpy as np
            from datetime import datetime, date
            
            if isinstance(data, list):
                return [convert_timestamps(item) for item in data]
            elif isinstance(data, dict):
                return {key: convert_timestamps(value) for key, value in data.items()}
            elif isinstance(data, pd.Timestamp):
                return data.isoformat()
            elif isinstance(data, (datetime, date)):
                return data.isoformat()
            elif hasattr(data, 'isoformat'):
                return data.isoformat()
            elif pd.isna(data) or (isinstance(data, float) and (np.isnan(data) or np.isinf(data))):
                return None  # Convert NaN/infinity to null for JSON
            else:
                return data
        
        # Prepare consolidated data with timestamp conversion
        logger.info("Preparing consolidated onboarding analytics data...")
        raw_insights_data = {
            'onboarding_completion': onboarding_completion[0] if onboarding_completion else None,
            'safari_permissions': safari_permissions[0] if safari_permissions else None,
            'metadata': {
                'analysis_date': execution_date,
                'onboarding_records': len(onboarding_completion) if onboarding_completion else 0,
                'safari_records': len(safari_permissions) if safari_permissions else 0,
                'generated_at': datetime.utcnow().isoformat()
            }
        }
        
        # Convert all timestamps to JSON-serializable format
        insights_data = convert_timestamps(raw_insights_data)
        
        total_records = sum([
            len(onboarding_completion) if onboarding_completion else 0,
            len(safari_permissions) if safari_permissions else 0
        ])
        logger.info(f"Total records to push: {total_records}")
        
        # Calculate payload size (with proper JSON serialization)
        import json
        
        def json_serializer(obj):
            """JSON serializer for objects not serializable by default json code"""
            import pandas as pd
            from datetime import datetime, date
            
            if isinstance(obj, pd.Timestamp):
                return obj.isoformat()
            elif isinstance(obj, (datetime, date)):
                return obj.isoformat()
            elif hasattr(obj, 'isoformat'):
                return obj.isoformat()
            return str(obj)
        
        payload_size = len(json.dumps(insights_data, default=json_serializer))
        logger.info(f"Payload size: {payload_size} bytes ({payload_size/1024:.2f} KB)")
        
        # Upsert to Supabase
        logger.info("Pushing consolidated results to Supabase...")
        logger.info(f"Table: onboarding_analytics")
        logger.info(f"Insight date: {execution_date}")
        logger.info(f"Insight type: daily_onboarding")
        
        result = supabase.table('onboarding_analytics').upsert({
            'analysis_date': execution_date,
            'insight_type': 'daily_onboarding',
            'data': insights_data
        }, on_conflict='analysis_date,insight_type').execute()
        
        logger.info(f"Supabase response: {result}")
        logger.info("Successfully pushed onboarding analytics data to Supabase")
        
        summary = {
            'status': 'success',
            'records_pushed': total_records,
            'payload_size_kb': round(payload_size/1024, 2),
            'components': {
                'onboarding_completion': len(onboarding_completion) if onboarding_completion else 0,
                'safari_permissions': len(safari_permissions) if safari_permissions else 0
            }
        }
        
        logger.info(f"=== PUSH_ONBOARDING_TO_SUPABASE SUCCESS ===")
        logger.info(f"Summary: {summary}")
        return summary
        
    except Exception as e:
        logger.error(f"=== PUSH_ONBOARDING_TO_SUPABASE ERROR ===")
        logger.error(f"Error type: {type(e)}")
        logger.error(f"Error message: {str(e)}")
        import traceback
        logger.error(f"Full traceback: {traceback.format_exc()}")
        raise