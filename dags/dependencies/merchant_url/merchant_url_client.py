"""
Merchant URL API client for fetching Google Cloud Logging data.
Handles fetching merchant URL creation logs and successful creation logs.
"""

import subprocess
import json
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional, Tuple

logger = logging.getLogger(__name__)


class MerchantURLClient:
    """Client for fetching merchant URL logs from Google Cloud Logging."""

    def __init__(self, project_id: str = "phia-prod-416420"):
        """
        Initialize the client with project configuration.
        
        Args:
            project_id: GCP project ID
        """
        logger.info("=== Initializing Merchant URL Client ===")
        self.project_id = project_id
        logger.info(f"Project ID: {self.project_id}")

    def fetch_creating_merchant_urls(
        self,
        start_date: str,
        end_date: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """
        Fetch logs for 'Creating merchant URL' messages.
        
        Args:
            start_date: Start date in YYYY-MM-DD format
            end_date: End date in YYYY-MM-DD format (optional, defaults to end of start_date)
            
        Returns:
            List of parsed log entries
        """
        logger.info("=== Fetching Creating Merchant URL Logs ===")
        logger.info(f"📅 Date range: {start_date} to {end_date or start_date}")
        
        timestamp_filter = self._build_timestamp_filter(start_date, end_date)
        
        command = [
            "gcloud", "logging", "read",
            f'jsonPayload.message:"Creating merchant URL" AND {timestamp_filter}',
            f"--project={self.project_id}",
            "--format=json"
        ]
        
        return self._execute_gcloud_command(command, "Creating merchant URL")

    def fetch_successful_merchant_urls(
        self,
        start_date: str,
        end_date: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """
        Fetch logs for 'Successfully created merchant URL' messages.
        
        Args:
            start_date: Start date in YYYY-MM-DD format
            end_date: End date in YYYY-MM-DD format (optional, defaults to end of start_date)
            
        Returns:
            List of parsed log entries
        """
        logger.info("=== Fetching Successful Merchant URL Logs ===")
        logger.info(f"📅 Date range: {start_date} to {end_date or start_date}")
        
        timestamp_filter = self._build_timestamp_filter(start_date, end_date)
        
        command = [
            "gcloud", "logging", "read",
            f'jsonPayload.message:"Successfully created merchant URL" AND {timestamp_filter}',
            f"--project={self.project_id}",
            "--format=json"
        ]
        
        return self._execute_gcloud_command(command, "Successfully created merchant URL")

    def _build_timestamp_filter(self, start_date: str, end_date: Optional[str] = None) -> str:
        """
        Build timestamp filter for gcloud logging query.
        
        Args:
            start_date: Start date in YYYY-MM-DD format
            end_date: End date in YYYY-MM-DD format (optional)
            
        Returns:
            Timestamp filter string
        """
        # Parse start date and add time
        start_datetime = datetime.strptime(start_date, "%Y-%m-%d")
        start_timestamp = start_datetime.strftime("%Y-%m-%dT00:00:00Z")
        
        if end_date:
            # Parse end date and set to end of day
            end_datetime = datetime.strptime(end_date, "%Y-%m-%d")
            end_timestamp = (end_datetime + timedelta(days=1)).strftime("%Y-%m-%dT00:00:00Z")
            return f'timestamp >= "{start_timestamp}" AND timestamp < "{end_timestamp}"'
        else:
            # If no end date, use end of start date
            end_timestamp = (start_datetime + timedelta(days=1)).strftime("%Y-%m-%dT00:00:00Z")
            return f'timestamp >= "{start_timestamp}" AND timestamp < "{end_timestamp}"'

    def _execute_gcloud_command(
        self,
        command: List[str],
        log_type: str
    ) -> List[Dict[str, Any]]:
        """
        Execute gcloud command and parse results.
        
        Args:
            command: gcloud command as list of arguments
            log_type: Type of logs being fetched (for logging)
            
        Returns:
            List of parsed log entries
        """
        logger.info(f"🚀 Executing gcloud command for {log_type}")
        logger.info(f"Command: {' '.join(command)}")
        
        try:
            # Execute command
            result = subprocess.run(
                command,
                capture_output=True,
                text=True,
                check=True
            )
            
            # Parse JSON output
            if result.stdout.strip():
                logs = json.loads(result.stdout)
                logger.info(f"✅ Successfully fetched {len(logs)} {log_type} logs")
                
                # Parse and extract relevant information
                parsed_logs = []
                for log_entry in logs:
                    parsed_entry = self._parse_log_entry(log_entry)
                    if parsed_entry:
                        parsed_logs.append(parsed_entry)
                
                logger.info(f"📊 Parsed {len(parsed_logs)} valid log entries")
                return parsed_logs
            else:
                logger.warning(f"⚠️ No logs found for {log_type}")
                return []
                
        except subprocess.CalledProcessError as e:
            logger.error(f"❌ gcloud command failed: {e}")
            logger.error(f"stderr: {e.stderr}")
            raise
        except json.JSONDecodeError as e:
            logger.error(f"❌ Failed to parse JSON output: {e}")
            raise
        except Exception as e:
            logger.error(f"❌ Unexpected error executing gcloud command: {e}")
            raise

    def _parse_log_entry(self, log_entry: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Parse a single log entry and extract relevant information.
        
        Args:
            log_entry: Raw log entry from gcloud
            
        Returns:
            Parsed log entry with relevant fields
        """
        try:
            # Extract basic fields
            parsed = {
                "timestamp": log_entry.get("timestamp"),
                "insertId": log_entry.get("insertId"),
                "message": log_entry.get("jsonPayload", {}).get("message", ""),
                "severity": log_entry.get("severity"),
                "resource": log_entry.get("resource", {}),
                "labels": log_entry.get("labels", {}),
                "jsonPayload": log_entry.get("jsonPayload", {})
            }
            
            # Extract additional fields from jsonPayload if available
            json_payload = log_entry.get("jsonPayload", {})
            if isinstance(json_payload, dict):
                # Add any additional fields that might be useful
                for key in ["url", "merchantUrl", "domain", "userId", "status", "error"]:
                    if key in json_payload:
                        parsed[key] = json_payload[key]
            
            return parsed
            
        except Exception as e:
            logger.warning(f"Failed to parse log entry: {e}")
            return None


def get_date_range_for_dag(execution_date: datetime) -> Tuple[str, str]:
    """
    Get date range for DAG execution based on execution date.
    
    Args:
        execution_date: Airflow execution date
        
    Returns:
        Tuple of (start_date, end_date) in YYYY-MM-DD format
    """
    # For catchup, use the execution date
    start_date = execution_date.strftime("%Y-%m-%d")
    end_date = start_date  # Single day range
    
    logger.info(f"📅 Date range for execution: {start_date} to {end_date}")
    return start_date, end_date


def get_yesterday_date_range() -> Tuple[str, str]:
    """
    Get yesterday's date range for daily runs.
    
    Returns:
        Tuple of (start_date, end_date) in YYYY-MM-DD format
    """
    yesterday = datetime.now() - timedelta(days=1)
    date_str = yesterday.strftime("%Y-%m-%d")
    
    logger.info(f"📅 Yesterday's date: {date_str}")
    return date_str, date_str