"""
Airflow tasks for merchant URL data pipeline.
"""

import logging
from typing import List, Dict, Any
from airflow.decorators import task
from airflow.providers.postgres.hooks.postgres import PostgresHook


from dependencies.merchant_url.merchant_url_client import MerchantUR<PERSON>lient
from dependencies.merchant_url.models import (

    MerchantURLCreationLog,
    MerchantURLSuccessLog
)

logger = logging.getLogger(__name__)


@task
def fetch_merchant_url_logs(ds: str) -> Dict[str, List[Dict[str, Any]]]:
    """
    Fetch merchant URL logs from Google Cloud Logging.
    
    Args:
        ds: Airflow execution date string in YYYY-MM-DD format
        
    Returns:
        Dictionary with 'creating' and 'successful' logs
    """
    logger.info("=== Starting Merchant URL Logs Fetch Task ===")
    logger.info(f"Processing data for execution date: {ds}")
    
    # Initialize client
    client = MerchantURLClient()
    
    # Use the execution date directly (ds is already in YYYY-MM-DD format)
    start_date = ds
    end_date = ds  # Same day range
    
    # Fetch both types of logs
    creating_logs = client.fetch_creating_merchant_urls(start_date, end_date)
    successful_logs = client.fetch_successful_merchant_urls(start_date, end_date)
    
    logger.info(f"✅ Fetched {len(creating_logs)} creating logs")
    logger.info(f"✅ Fetched {len(successful_logs)} successful logs")
    
    return {
        "creating": creating_logs,
        "successful": successful_logs,
        "date": start_date
    }


@task
def transform_merchant_url_data(logs_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Transform raw logs into structured data.
    
    Args:
        logs_data: Dictionary with creating and successful logs
        
    Returns:
        Dictionary with transformed data
    """
    logger.info("=== Starting Merchant URL Data Transformation ===")
    
    creating_logs = logs_data["creating"]
    successful_logs = logs_data["successful"]
    
    # Transform logs into model objects
    creation_logs = [
        MerchantURLCreationLog.from_dict(log) 
        for log in creating_logs
    ]
    
    success_logs = [
        MerchantURLSuccessLog.from_dict(log)
        for log in successful_logs
    ]
    
    logger.info(f"📊 Transformation complete:")
    logger.info(f"   - Creation logs: {len(creation_logs)}")
    logger.info(f"   - Success logs: {len(success_logs)}")
    
    return {
        "creation_logs": [log.to_dict() for log in creation_logs],
        "success_logs": [log.to_dict() for log in success_logs]
    }


@task
def load_to_supabase(transformed_data: Dict[str, Any]) -> Dict[str, int]:
    """
    Load transformed data to Supabase tables.
    
    Args:
        transformed_data: Dictionary with transformed logs
        
    Returns:
        Dictionary with counts of loaded records
    """
    logger.info("=== Starting Load to Supabase ===")
    
    # Get Supabase connection
    pg_hook = PostgresHook(postgres_conn_id="supabase_conn")
    
    creation_logs = transformed_data["creation_logs"]
    success_logs = transformed_data["success_logs"]
    
    # Prepare batch insert with conflict handling
    inserted_creation = 0
    inserted_success = 0
    
    # Insert creation logs with ON CONFLICT handling
    if creation_logs:
        logger.info(f"💾 Inserting {len(creation_logs)} creation logs")
        
        for log in creation_logs:
            try:
                pg_hook.run(
                    """
                    INSERT INTO merchant_url_creation_logs (
                        timestamp, insert_id, message, severity, resource,
                        labels, json_payload, url, merchant_url, domain,
                        user_id, status, error
                    ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                    ON CONFLICT (insert_id) DO NOTHING
                    """,
                    parameters=(
                        log["timestamp"],
                        log["insert_id"],
                        log["message"],
                        log["severity"],
                        log["resource"],
                        log["labels"],
                        log["json_payload"],
                        log.get("url"),
                        log.get("merchant_url"),
                        log.get("domain"),
                        log.get("user_id"),
                        log.get("status"),
                        log.get("error")
                    )
                )
                inserted_creation += 1
            except Exception as e:
                logger.warning(f"Failed to insert creation log {log['insert_id']}: {e}")
    
    # Insert success logs with ON CONFLICT handling
    if success_logs:
        logger.info(f"💾 Inserting {len(success_logs)} success logs")
        
        for log in success_logs:
            try:
                pg_hook.run(
                    """
                    INSERT INTO merchant_url_success_logs (
                        timestamp, insert_id, message, severity, resource,
                        labels, json_payload, url, merchant_url, domain,
                        user_id, status, error
                    ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                    ON CONFLICT (insert_id) DO NOTHING
                    """,
                    parameters=(
                        log["timestamp"],
                        log["insert_id"],
                        log["message"],
                        log["severity"],
                        log["resource"],
                        log["labels"],
                        log["json_payload"],
                        log.get("url"),
                        log.get("merchant_url"),
                        log.get("domain"),
                        log.get("user_id"),
                        log.get("status"),
                        log.get("error")
                    )
                )
                inserted_success += 1
            except Exception as e:
                logger.warning(f"Failed to insert success log {log['insert_id']}: {e}")
    
    logger.info(f"✅ Data loaded successfully")
    logger.info(f"   - Creation logs inserted: {inserted_creation}")
    logger.info(f"   - Success logs inserted: {inserted_success}")
    
    return {
        "creation_logs_loaded": inserted_creation,
        "success_logs_loaded": inserted_success
    }


@task
def validate_data_quality(load_results: Dict[str, int], transformed_data: Dict[str, Any]) -> bool:
    """
    Validate that data was loaded correctly.
    
    Args:
        load_results: Results from the load task
        transformed_data: Transformed data for comparison
        
    Returns:
        True if validation passes
    """
    logger.info("=== Starting Data Quality Validation ===")
    
    expected_creation = len(transformed_data["creation_logs"])
    expected_success = len(transformed_data["success_logs"])
    
    actual_creation = load_results["creation_logs_loaded"]
    actual_success = load_results["success_logs_loaded"]
    
    # Allow for some records to be skipped due to duplicates
    creation_threshold = 0.95  # 95% success rate acceptable
    success_threshold = 0.95  # 95% success rate acceptable
    
    validation_passed = True
    
    creation_rate = actual_creation / expected_creation if expected_creation > 0 else 1.0
    if creation_rate < creation_threshold:
        logger.warning(
            f"⚠️  Creation logs below threshold: expected {expected_creation}, "
            f"loaded {actual_creation} ({creation_rate:.2%})"
        )
        # Don't fail on this, as duplicates are expected during catchup
    else:
        logger.info(f"✅ Creation logs validated: {actual_creation}/{expected_creation} records")
    
    success_rate = actual_success / expected_success if expected_success > 0 else 1.0
    if success_rate < success_threshold:
        logger.warning(
            f"⚠️  Success logs below threshold: expected {expected_success}, "
            f"loaded {actual_success} ({success_rate:.2%})"
        )
        # Don't fail on this, as duplicates are expected during catchup
    else:
        logger.info(f"✅ Success logs validated: {actual_success}/{expected_success} records")
    
    logger.info("✅ Data quality check completed")
    return validation_passed