"""
Data models for merchant URL logs.
"""

from dataclasses import dataclass
from typing import Optional, Dict, Any


@dataclass
class MerchantURLLog:
    """Base model for merchant URL log entries."""
    timestamp: str
    insert_id: str
    message: str
    severity: str
    resource: Dict[str, Any]
    labels: Dict[str, Any]
    json_payload: Dict[str, Any]
    
    # Optional fields that might be extracted from jsonPayload
    url: Optional[str] = None
    merchant_url: Optional[str] = None
    domain: Optional[str] = None
    user_id: Optional[str] = None
    status: Optional[str] = None
    error: Optional[str] = None
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "MerchantURLLog":
        """Create MerchantURLLog from dictionary."""
        return cls(
            timestamp=data.get("timestamp", ""),
            insert_id=data.get("insertId", ""),
            message=data.get("message", ""),
            severity=data.get("severity", ""),
            resource=data.get("resource", {}),
            labels=data.get("labels", {}),
            json_payload=data.get("jsonPayload", {}),
            url=data.get("url"),
            merchant_url=data.get("merchantUrl"),
            domain=data.get("domain"),
            user_id=data.get("userId"),
            status=data.get("status"),
            error=data.get("error")
        )
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for storage."""
        return {
            "timestamp": self.timestamp,
            "insert_id": self.insert_id,
            "message": self.message,
            "severity": self.severity,
            "resource": self.resource,
            "labels": self.labels,
            "json_payload": self.json_payload,
            "url": self.url,
            "merchant_url": self.merchant_url,
            "domain": self.domain,
            "user_id": self.user_id,
            "status": self.status,
            "error": self.error
        }


@dataclass
class MerchantURLCreationLog(MerchantURLLog):
    """Model for 'Creating merchant URL' logs."""
    pass


@dataclass
class MerchantURLSuccessLog(MerchantURLLog):
    """Model for 'Successfully created merchant URL' logs."""
    pass