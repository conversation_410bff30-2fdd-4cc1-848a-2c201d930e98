from google.cloud import bigquery
from datetime import datetime
from typing import Dict
import re
import json

# We are using Auto detection of schema using initial occurrences
# This function is used to ensure few edge cases
def schema_update_required(event_type, obj):
    """Ensures the event payload is in the correct format"""
    try:
        if event_type == "SEARCH":
            fv = obj["filters"]["searchFilters"]["filterValues"]
            if isinstance(fv, list):
                obj["filters"]["searchFilters"]["filterValues"] = [str(x) for x in fv]
            else:
                obj["filters"]["searchFilters"]["filterValues"] = str(fv)

        if event_type == "RESALE_INSIGHTS_DATA_LOADED":
            resale = obj.setdefault("resale_data", {})
            expensive = resale.get("expensiveBestMatchProducts")
            if not isinstance(expensive, list):
                expensive = []
                resale["expensiveBestMatchProducts"] = expensive

            for prod in expensive:
                raw = prod.get("sizeDisplayName", None)
                if isinstance(raw, str):
                    prod["sizeDisplayName"] = raw
                else:
                    prod["sizeDisplayName"] = str(raw) if raw is not None else ""

    except (KeyError, TypeError):
        pass
    return obj

def update_metadata(client: bigquery.Client, metadata_table: str, event_type: str, last_ts: str):
    """
    Upserts the last_processed_timestamp for event_type into metadata_table.
    """
    merge_sql = f"""
    MERGE `{metadata_table}` T
    USING (
      SELECT '{event_type}' AS event_type,
             '{last_ts}' AS last_processed_timestamp
    ) S
    ON T.event_type = S.event_type
    WHEN MATCHED THEN
      UPDATE SET last_processed_timestamp = S.last_processed_timestamp
    WHEN NOT MATCHED THEN
      INSERT(event_type, last_processed_timestamp)
      VALUES(S.event_type, S.last_processed_timestamp)
    """
    client.query(merge_sql).result()

def sanitize_json(raw: str) -> str:
    """
    Escape any unescaped " inside JSON string literals in one pass.
    Only treats a " as closing when the next non-space is one of :, , , }, or ].
    """
    out = []
    in_str = False
    i = 0
    n = len(raw)

    while i < n:
        c = raw[i]

        if in_str:
            # If we hit a backslash
            if c == '\\' and i + 1 < n:
                out.append(raw[i])
                out.append(raw[i+1])
                i += 2
                continue

            # Unescaped quote
            if c == '"':
                # look ahead past any whitespace
                j = i + 1
                while j < n and raw[j].isspace():
                    j += 1

                # if next char is one of :, , , }, or ], this really closes the string
                if j < n and raw[j] in {':', ',', '}', ']'}:
                    in_str = False
                    out.append(c)
                else:
                    # escape it
                    out.append(r'\"')
                i += 1
                continue

            # all other chars inside a string
            out.append(c)
            i += 1

        else:
            # outside a string, a " always opens one
            if c == '"':
                in_str = True
            out.append(c)
            i += 1

    return ''.join(out)


def update_props(raw: str, event_name: str) -> str:
    # as per observation, these are most noisy props
    if event_name == "EXTENSION_PAGE_VIEW":
        pattern = re.compile(r',?\s*"categoryFeatures"\s*:\s*\[[^\]]*\]\s*,?\\?', flags=re.DOTALL)
        s = pattern.sub(',', raw).replace("\\,", "").replace("\\\"", "")
        return s
    end_list = raw + ']}'
    try:
        json.loads(end_list)
        return end_list
    except json.JSONDecodeError:
        end_obj = raw + '}'
        try:
            json.loads(end_obj)
            return end_obj
        except json.JSONDecodeError:
            end_obj = raw + '}}'
            try:
                json.loads(end_obj)
                return end_obj
            except json.JSONDecodeError:
                # if all fails, return the raw string
                print(f"Failed to update props for event {event_name}, returning raw")
                return raw

def clean_string(s):
    # replace surrogates with U+FFFD
    return s.encode('utf-8', 'replace').decode('utf-8', 'replace')

def sanitize_obj(o):
    if isinstance(o, str):
        return clean_string(o)
    if isinstance(o, dict):
        return {k: sanitize_obj(v) for k, v in o.items()}
    if isinstance(o, list):
        return [sanitize_obj(v) for v in o]
    return o                                