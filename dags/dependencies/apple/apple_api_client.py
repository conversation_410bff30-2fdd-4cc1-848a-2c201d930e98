"""
Apple App Store Connect Analytics API Client

This client implements the complete workflow for generating and downloading
analytics reports from Apple's App Store Connect API. It follows the exact
sequence required by Apple's API:

1. Create report request (POST)
2. Get reports list from request
3. Get instances from report
4. Get segments from instance
5. Download data from segment URL

The client integrates with the existing JWT service for authentication.
"""

import os
import requests
import logging
import time
import gzip
import zipfile
import io
from typing import Dict, Any, Optional, List, Union
from datetime import datetime, timedelta
from google.cloud import secretmanager

from .jwt_service import AppleJWTService

logger = logging.getLogger(__name__)


class AppleAnalyticsClient:
    """Client for Apple App Store Connect Analytics Reports API."""
    
    BASE_URL = "https://api.appstoreconnect.apple.com/v1"
    
    def __init__(self, app_id: Optional[str] = None):
        """
        Initialize the client with JWT service and app configuration.
        
        Args:
            app_id: Apple App ID (e.g., "6739351340"). If not provided, uses APPLE_APP_ID env var.
        """
        logger.info("🔧 Initializing Apple Analytics client...")
        logger.info("🔑 Initializing JWT service...")
        self.jwt_service = AppleJWTService()
        logger.info("✅ JWT service initialized successfully")
        
        if app_id:
            logger.info(f"📋 Using provided app_id: {app_id[:10]}...")
            self.app_id = app_id
        else:
            logger.info("🔑 Fetching app_id from credentials...")
            self.app_id = self._get_credential("APPLE_APP_ID", "apple-app-id")
            logger.info(f"✅ Retrieved app_id from credentials: {self.app_id[:10] if self.app_id else 'None'}...")
        
        self.session = requests.Session()
        self.session.timeout = 30
        
        if not self.app_id:
            logger.error("❌ Apple App ID is missing from both parameter and credentials")
            raise ValueError("Apple App ID is required. Set APPLE_APP_ID environment variable or pass app_id parameter.")
            
        logger.info(f"🍎 Apple Analytics client initialized for app ID: {self.app_id}")
    
    def _get_credential(self, env_var: str, secret_name: str) -> str:
        """Get credential from environment variable or Secret Manager."""
        logger.info(f"Getting credential for {secret_name}...")

        # Try environment variable first
        value = os.getenv(env_var)
        if value:
            logger.info(f"✅ Retrieved credential {secret_name} from environment variable")
            return value
            
        # Try Secret Manager
        try:
            logger.info(f"🔍 Attempting to retrieve credential {secret_name} from Secret Manager")
            start_time = time.time()

            client = secretmanager.SecretManagerServiceClient()
            project_id = "609155540540"
            secret_path = f"projects/{project_id}/secrets/{secret_name}/versions/latest"

            logger.info(f"Secret path: {secret_path}")
            response = client.access_secret_version(request={"name": secret_path})

            secret_time = time.time() - start_time
            logger.info(f"✅ Successfully retrieved credential {secret_name} from Secret Manager in {secret_time:.2f} seconds")

            return response.payload.data.decode("UTF-8")

        except Exception as e:
            logger.error(f"❌ Failed to get credential {secret_name} from Secret Manager: {e}")
            logger.error(f"Exception type: {type(e).__name__}")
            return ""
    
    def _make_request(self, method: str, endpoint: str, json_data: Optional[Dict] = None, 
                     max_retries: int = 3, **kwargs) -> requests.Response:
        """
        Make an authenticated request to the Apple API with retry logic.
        
        Args:
            method: HTTP method (GET, POST, etc.)
            endpoint: API endpoint path
            json_data: JSON payload for POST requests
            max_retries: Maximum number of retry attempts
            **kwargs: Additional arguments passed to requests
            
        Returns:
            Response object
            
        Raises:
            requests.RequestException: If request fails after all retries
        """
        url = f"{self.BASE_URL}/{endpoint.lstrip('/')}"
        
        for attempt in range(max_retries + 1):
            try:
                # Get JWT token
                token = self.jwt_service.generate_jwt_token()
                
                # Set headers
                headers = {
                    "Authorization": f"Bearer {token}",
                    "Content-Type": "application/json"
                }
                
                # Log request details
                logger.info(f"🔄 {method} {url} (attempt {attempt + 1}/{max_retries + 1})")
                if json_data:
                    logger.debug(f"Request payload: {json_data}")
                
                # Make request
                response = self.session.request(
                    method=method,
                    url=url,
                    headers=headers,
                    json=json_data,
                    **kwargs
                )
                
                # Log response
                logger.info(f"📡 Response: {response.status_code}")
                logger.debug(f"Response headers: {dict(response.headers)}")
                
                # Handle rate limiting
                if response.status_code == 429:
                    retry_after = int(response.headers.get("Retry-After", 60))
                    logger.warning(f"⏱️ Rate limited. Waiting {retry_after} seconds...")
                    time.sleep(retry_after)
                    continue
                
                # Check for success
                response.raise_for_status()
                
                return response
                
            except requests.RequestException as e:
                logger.error(f"❌ Request failed: {e}")
                
                if hasattr(e, 'response') and e.response is not None:
                    logger.error(f"Error response: {e.response.text}")
                
                if attempt < max_retries:
                    wait_time = 2 ** attempt
                    logger.info(f"⏳ Retrying in {wait_time} seconds...")
                    time.sleep(wait_time)
                else:
                    raise
    
    def create_report_request(self, access_type: str = "ONE_TIME_SNAPSHOT") -> str:
        """
        Step 1: Create an analytics report request for the specified app.
        
        Args:
            access_type: Type of report access:
                - "ONE_TIME_SNAPSHOT": For historical data (default)
                - "ONGOING": For continuous daily updates
                
        Returns:
            Report request ID (e.g., "3bfe16a0-ded2-444c-82fb-ec819d301f01")
            
        Raises:
            requests.RequestException: If request creation fails
        """
        logger.info(f"📊 Creating analytics report request with access type: {access_type}")
        
        # Construct the payload according to Apple's API
        payload = {
            "data": {
                "type": "analyticsReportRequests",
                "attributes": {
                    "accessType": access_type
                },
                "relationships": {
                    "app": {
                        "data": {
                            "type": "apps",
                            "id": self.app_id
                        }
                    }
                }
            }
        }
        
        # Make the POST request
        response = self._make_request("POST", "/analyticsReportRequests", json_data=payload)
        
        # Extract the report request ID
        data = response.json()
        report_request_id = data["data"]["id"]
        
        logger.info(f"✅ Created report request: {report_request_id}")
        return report_request_id
    
    def get_reports(self, report_request_id: str) -> str:
        """
        Step 2: Get the list of reports for a given report request.
        Returns the first report ID that starts with 'r'.
        
        Args:
            report_request_id: The report request ID from create_report_request
            
        Returns:
            Report ID (e.g., "r7-3bfe16a0-ded2-444c-82fb-ec819d301f01")
            
        Raises:
            ValueError: If no report with ID starting with 'r' is found
        """
        logger.info(f"📋 Fetching reports for request: {report_request_id}")
        
        endpoint = f"/analyticsReportRequests/{report_request_id}/reports"
        response = self._make_request("GET", endpoint)
        
        data = response.json()
        reports = data.get("data", [])
        
        # Find the report ID that starts with 'r'
        for report in reports:
            if report["id"].startswith("r"):
                report_id = report["id"]
                report_name = report.get("attributes", {}).get("name", "Unknown")
                logger.info(f"✅ Found report: {report_id} ({report_name})")
                return report_id
        
        # If no report found, list what we got
        report_ids = [r["id"] for r in reports]
        logger.error(f"❌ No report ID starting with 'r' found. Available reports: {report_ids}")
        raise ValueError(f"No report ID starting with 'r' found in {len(reports)} reports")
    
    def get_instances(self, report_id: str) -> str:
        """
        Step 3: Get instances for a given report ID.
        Returns the most recent instance ID found.
        
        Args:
            report_id: The report ID (starting with 'r')
            
        Returns:
            Instance ID (e.g., "dc46bf02-5cc2-4cfc-8f0a-3aef94d6dd37")
            
        Raises:
            ValueError: If no instances are found
        """
        logger.info(f"📦 Fetching instances for report: {report_id}")
        
        endpoint = f"/analyticsReports/{report_id}/instances"
        response = self._make_request("GET", endpoint)
        
        data = response.json()
        instances = data.get("data", [])
        
        if not instances:
            logger.error("❌ No instances found for report")
            raise ValueError(f"No instances found for report {report_id}")
        
        # Sort instances by processing date (newest first)
        sorted_instances = sorted(
            instances,
            key=lambda x: x.get("attributes", {}).get("processingDate", ""),
            reverse=True
        )
        
        # Get the most recent instance
        latest_instance = sorted_instances[0]
        instance_id = latest_instance["id"]
        processing_date = latest_instance.get("attributes", {}).get("processingDate", "Unknown")
        
        # Log some info about available instances
        logger.info(f"📊 Found {len(instances)} instances for this report")
        logger.info(f"✅ Selected most recent instance: {instance_id} (processed: {processing_date})")
        
        # Show a few more instances for context
        if len(sorted_instances) > 1:
            for i, inst in enumerate(sorted_instances[1:4], 1):  # Show next 3
                date = inst.get("attributes", {}).get("processingDate", "Unknown")
                logger.info(f"   Previous instance {i}: {date}")
        
        return instance_id
    
    def get_segments(self, instance_id: str) -> str:
        """
        Step 4: Get segments for a given instance ID.
        Returns the download URL for the first segment.
        
        Args:
            instance_id: The instance ID
            
        Returns:
            Segment download URL
            
        Raises:
            ValueError: If no segments are found
        """
        logger.info(f"🔗 Fetching segments for instance: {instance_id}")
        
        endpoint = f"/analyticsReportInstances/{instance_id}/segments"
        response = self._make_request("GET", endpoint)
        
        data = response.json()
        segments = data.get("data", [])
        
        if not segments:
            logger.error("❌ No segments found for instance")
            raise ValueError(f"No segments found for instance {instance_id}")
        
        # Get the download URL from the first segment
        segment = segments[0]
        segment_url = segment["attributes"]["url"]
        size_bytes = segment["attributes"].get("sizeInBytes", 0)
        
        logger.info(f"✅ Found segment: {size_bytes:,} bytes")
        return segment_url
    
    def download_segment(self, segment_url: str, save_path: Optional[str] = None) -> Union[bytes, str]:
        """
        Step 5: Download the report data from the segment URL.
        
        Args:
            segment_url: The segment download URL
            save_path: Optional path to save the downloaded file.
                      If not provided, returns the raw bytes.
                      
        Returns:
            If save_path is provided: Path to the saved file
            If save_path is not provided: Raw bytes of the downloaded content
            
        Raises:
            requests.RequestException: If download fails
        """
        logger.info(f"⬇️ Downloading segment data...")
        
        # S3 URLs are pre-signed and don't need authentication headers
        # In fact, adding them causes errors
        response = self.session.get(segment_url, stream=True)
        response.raise_for_status()
        
        # Get content info
        content_length = int(response.headers.get('content-length', 0))
        content_type = response.headers.get('content-type', 'unknown')
        
        logger.info(f"📦 Downloading {content_length:,} bytes ({content_type})")
        
        if save_path:
            # Save to file
            with open(save_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
            logger.info(f"💾 Saved report to: {save_path}")
            return save_path
        else:
            # Return raw bytes
            content = response.content
            logger.info(f"✅ Downloaded {len(content):,} bytes")
            return content
    
    def download_analytics_report(self, access_type: str = "ONE_TIME_SNAPSHOT", 
                                save_path: Optional[str] = None,
                                max_wait_minutes: int = 30,
                                poll_interval_seconds: int = 60) -> Union[bytes, str]:
        """
        Complete workflow to download analytics report data.
        Combines all steps into a single method.
        
        Args:
            access_type: "ONE_TIME_SNAPSHOT" or "ONGOING"
            save_path: Optional path to save the downloaded file
            max_wait_minutes: Maximum time to wait for report generation
            poll_interval_seconds: Interval between status checks
            
        Returns:
            Downloaded report data (bytes) or path to saved file
            
        Raises:
            Exception: If any step in the process fails
        """
        logger.info(f"🚀 Starting complete analytics report download process")
        
        try:
            # Step 1: Create report request
            report_request_id = self.create_report_request(access_type)
            
            # Wait a bit for initial processing
            logger.info("⏳ Waiting for initial report processing...")
            time.sleep(10)
            
            # Poll for report availability
            start_time = time.time()
            max_wait_seconds = max_wait_minutes * 60
            report_id = None
            
            while time.time() - start_time < max_wait_seconds:
                try:
                    # Step 2: Try to get reports
                    report_id = self.get_reports(report_request_id)
                    break
                except ValueError as e:
                    # Reports not ready yet
                    elapsed = int(time.time() - start_time)
                    logger.info(f"⏳ Reports not ready yet. Elapsed: {elapsed}s / {max_wait_seconds}s")
                    time.sleep(poll_interval_seconds)
            
            if not report_id:
                raise TimeoutError(f"Report not available after {max_wait_minutes} minutes")
            
            # Step 3: Get instances
            instance_id = self.get_instances(report_id)
            
            # Step 4: Get segments
            segment_url = self.get_segments(instance_id)
            
            # Step 5: Download data
            result = self.download_segment(segment_url, save_path)
            
            logger.info(f"🎉 Successfully completed analytics report download!")
            return result
            
        except Exception as e:
            logger.error(f"❌ Failed to download analytics report: {e}")
            raise
    
    def extract_report_data(self, report_bytes: bytes) -> Dict[str, Any]:
        """
        Extract and parse report data from downloaded bytes.
        Handles both gzip and zip compressed formats.
        
        Args:
            report_bytes: Raw bytes of the downloaded report
            
        Returns:
            Dictionary containing the extracted report data
        """
        logger.info("📂 Extracting report data...")
        
        try:
            # Try gzip first
            try:
                decompressed = gzip.decompress(report_bytes)
                logger.info("✅ Decompressed gzip data")
                # Assume it's CSV or similar text format
                return {"format": "gzip", "data": decompressed.decode('utf-8')}
            except:
                pass
            
            # Try zip
            try:
                with zipfile.ZipFile(io.BytesIO(report_bytes)) as zf:
                    files = {}
                    for name in zf.namelist():
                        files[name] = zf.read(name).decode('utf-8')
                    logger.info(f"✅ Extracted {len(files)} files from zip")
                    return {"format": "zip", "files": files}
            except:
                pass
            
            # If not compressed, assume raw data
            logger.info("📄 Treating as uncompressed data")
            return {"format": "raw", "data": report_bytes.decode('utf-8')}
            
        except Exception as e:
            logger.error(f"❌ Failed to extract report data: {e}")
            raise