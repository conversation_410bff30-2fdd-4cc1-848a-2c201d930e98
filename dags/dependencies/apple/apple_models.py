"""
Apple Installation/Deletion Data Models

This module defines simplified Pydantic models for Apple App Store Installation and Deletion
reports, mapping directly to the CSV structure for simple storage in Supabase.
"""

import hashlib
import logging
from datetime import datetime
from typing import Dict, Any, Optional, List
from pydantic import BaseModel, Field, validator
from pydantic.dataclasses import dataclass

logger = logging.getLogger(__name__)


class AppleInstallationDeletionData(BaseModel):
    """Simple model for Apple Installation/Deletion Standard CSV data."""
    
    # Core fields (required)
    date: str = Field(..., description="Report date in YYYY-MM-DD format")
    app_name: str = Field(..., description="Application name")
    app_apple_identifier: str = Field(..., description="Apple App Store ID")
    event: str = Field(..., description="Event type (Install, Delete, etc.)")
    app_version: str = Field(..., description="App version")
    device: str = Field(..., description="Device type (iPhone, iPad, etc.)")
    platform_version: str = Field(..., description="iOS/iPadOS version")
    source_type: str = Field(..., description="Traffic source type")
    territory: str = Field(..., description="Country/territory code")
    counts: int = Field(..., description="Event count")
    unique_devices: int = Field(..., description="Unique device count")
    
    # Optional fields
    download_type: Optional[str] = Field(None, description="Type of download")
    page_type: Optional[str] = Field(None, description="Page type")
    app_download_date: Optional[str] = Field(None, description="App download date")
    
    # Metadata
    unique_id: Optional[str] = Field(None, description="Generated unique identifier")
    created_at: Optional[datetime] = Field(default_factory=datetime.utcnow, description="Record creation timestamp")
    
    @validator('date', pre=True)
    def validate_date(cls, v):
        """Validate date format."""
        try:
            datetime.strptime(v, '%Y-%m-%d')
            return v
        except ValueError:
            raise ValueError(f"Invalid date format: {v}")

    @validator('counts', 'unique_devices', pre=True)
    def validate_positive_counts(cls, v):
        """Ensure counts are positive."""
        if v < 0:
            raise ValueError("Counts must be non-negative")
        return v

    @validator('app_apple_identifier', pre=True) 
    def validate_app_id(cls, v):
        """Validate Apple App ID format."""
        if not str(v).isdigit() or len(str(v)) < 8:
            raise ValueError(f"Invalid Apple App ID: {v}")
        return str(v)
    
    def generate_unique_id(self) -> str:
        """Generate a unique identifier for this record."""
        key_fields = [
            self.date,
            self.app_apple_identifier,
            self.event,
            self.download_type or "",
            self.app_version,
            self.device,
            self.platform_version,
            self.source_type,
            self.page_type or "",
            self.app_download_date or "",
            self.territory,
            str(self.counts),
            str(self.unique_devices)
        ]
        
        composite_key = "|".join(key_fields)
        return hashlib.sha256(composite_key.encode()).hexdigest()[:32]
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for Supabase insertion."""
        # Generate unique_id if not set
        if not self.unique_id:
            self.unique_id = self.generate_unique_id()
            
        data = self.dict()
        
        # Convert datetime objects to ISO strings
        for key, value in data.items():
            if isinstance(value, datetime):
                data[key] = value.isoformat()
        
        return data
    
    class Config:
        """Pydantic configuration."""
        str_strip_whitespace = True
        validate_assignment = True

# Keep for backward compatibility - alias to new simple model
AppleAnalyticsRawData = AppleInstallationDeletionData
AppleInstallationData = AppleInstallationDeletionData


def parse_csv_row_simple(row: Dict[str, str]) -> Optional[AppleInstallationDeletionData]:
    """Parse a CSV row into AppleInstallationDeletionData."""
    try:
        # Map CSV columns to model fields
        csv_mapping = {
            'Date': 'date',
            'App Name': 'app_name', 
            'App Apple Identifier': 'app_apple_identifier',
            'Event': 'event',
            'Download Type': 'download_type',
            'App Version': 'app_version',
            'Device': 'device',
            'Platform Version': 'platform_version',
            'Source Type': 'source_type',
            'Page Type': 'page_type',
            'App Download Date': 'app_download_date',
            'Territory': 'territory',
            'Counts': 'counts',
            'Unique Devices': 'unique_devices',
        }
        
        # Map fields from CSV
        mapped_data = {}
        for csv_field, model_field in csv_mapping.items():
            value = row.get(csv_field, '').strip()
            if value == '':
                value = None
            mapped_data[model_field] = value
        
        # Convert numeric fields
        try:
            mapped_data['counts'] = int(mapped_data['counts'] or 0)
            mapped_data['unique_devices'] = int(mapped_data['unique_devices'] or 0)
        except (ValueError, TypeError) as e:
            logger.warning(f"Invalid numeric value in row: {row}, error: {e}")
            return None
        
        # Create and return the record
        record = AppleInstallationDeletionData(**mapped_data)
        record.unique_id = record.generate_unique_id()
        
        return record
        
    except Exception as e:
        logger.error(f"Failed to parse CSV row: {e}")
        logger.error(f"Row data: {row}")
        return None


def transform_csv_data_simple(csv_content: str) -> List[AppleInstallationDeletionData]:
    """Transform CSV content to list of AppleInstallationDeletionData objects."""
    import csv
    import io
    
    transformed_data = []
    failed_count = 0
    
    try:
        # Parse CSV
        csv_reader = csv.DictReader(io.StringIO(csv_content), delimiter='\t')
        
        for row_num, row in enumerate(csv_reader, 1):
            record = parse_csv_row_simple(row)
            if record:
                transformed_data.append(record)
            else:
                failed_count += 1
        
        logger.info(f"Processed {len(transformed_data)} records successfully, {failed_count} failed")
        return transformed_data
        
    except Exception as e:
        logger.error(f"Failed to transform CSV data: {e}")
        return []


# Keep old function names for backward compatibility
def transform_csv_data(csv_content: str, report_name: str = "") -> List[AppleInstallationDeletionData]:
    """Transform CSV content - simplified version."""
    return transform_csv_data_simple(csv_content)