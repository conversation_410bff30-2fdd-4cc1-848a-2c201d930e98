from typing import Optional, List
from google.cloud import bigquery
import pandas as pd

# Network-specific constants
SKIMLINKS_NETWORK_RANK: int = 13
SHOPMY_NETWORK_RANK: int = 14
WILDFIRE_NETWORK_RANK: int = 15
SOVRN_NETWORK_RANK: int = 16
DEFAULT_COOKIE_DURATION: str = '720'

# Network names
SHOPMY_NETWORK_NAME: str = 'ShopMy'
WILDFIRE_NETWORK_NAME: str = 'Wildfire'
SOVRN_NETWORK_NAME: str = 'Sovrn'
SKIMLINKS_NETWORK_NAME: str = 'Skimlinks'

# Device types
DEVICE_DESKTOP: str = 'DESKTOP'
DEVICE_MOBILE: str = 'MOBILE'
DEVICE_BOTH: str = 'BOTH'

def get_bigquery_schema() -> List[bigquery.SchemaField]:
    """Get BigQuery schema for affiliate data."""
    return [
        bigquery.SchemaField("networkRank", "INTEGER"),
        bigquery.SchemaField("networkName", "STRING"),
        bigquery.SchemaField("AdvertiserId", "STRING"),
        bigquery.SchemaField("Name", "STRING"),
        bigquery.SchemaField("Website", "STRING"),
        bigquery.SchemaField("domain", "STRING"),
        bigquery.SchemaField("commissionRateMax", "FLOAT"),
        bigquery.SchemaField("evergreenUrl", "STRING"),
        bigquery.SchemaField("evergreenUrlOverride", "STRING"),
        bigquery.SchemaField("cookieDurationHours", "STRING"),
        bigquery.SchemaField("evergreenUrlLastTestedAt", "STRING"),
        bigquery.SchemaField("evergreenUrlTestStatus", "STRING"),
        bigquery.SchemaField("device", "STRING")
    ] 