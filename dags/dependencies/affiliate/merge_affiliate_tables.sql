CREATE OR REPLACE TABLE {{ params.affiliate_table_id }} AS
WITH ranked_networks AS (
    SELECT 
        'eBay Partner Network' AS network_name, 1 AS networkRank
    UNION ALL
    SELECT 
        'Impact.com', 2
    UNION ALL
    SELECT 
        'Rakuten Advertising', 3
    UNION ALL
    SELECT 
        'CJ Affiliate', 4
    UNION ALL
    SELECT 
        'Awin', 5
    UNION ALL
    SELECT 
        'Partnerize', 6
    UNION ALL
    SELECT 
        'Ascend by Partnerize', 7
    UNION ALL
    SELECT 
        'ShareASale', 8
    UNION ALL
    SELECT 
        'FlexOffers', 9
    UNION ALL
    SELECT
        'digidip', 10
),

linkbuilder AS (
    SELECT
        DISTINCT
        advertisers.id,
        program.status_id AS programStatus,
        advertisers.network_name AS networkName,
        REPLACE(LOWER(advertisers.network_name), ' ', '_') AS networkId,
        advertisers.source_id AS advertiserId,
        advertisers.name AS name,
        LOWER(REPLACE(advertisers.website, 'http://', 'https://')) AS website,
        LOWER(REGEXP_REPLACE(advertisers.website, r'^https?://(www\.)?', '')) AS domainWithPath,
        REGEXP_REPLACE(LOWER(advertisers.website), r'^https?://(www\.)?|/.*$', '') AS domain,
        REPLACE(link.url, 'http://', 'https://') AS linkWebsite,
        LOWER(REGEXP_REPLACE(link.url, r'^https?://(www\.)?', '')) AS linkDomainWithPath,
        REGEXP_REPLACE(LOWER(link.url), r'^https?://(www\.)?|/.*$', '') AS linkDomain,
        link.url AS linkUrl,
        COALESCE(link.trackinglink, missing.evergreenUrlOverride) AS linkBuilderUrl,
        CASE 
            -- For encoded homepage URLs (e.g., Poshmark, The RealReal)
            WHEN link.trackinglink LIKE CONCAT('%', REPLACE(REPLACE(link.url, ':', '%3A'), '/', '%2F'), '%') 
                THEN REPLACE(REPLACE(link.url, ':', '%3A'), '/', '%2F')
            -- For non-encoded homepage URLs (e.g., eBay)
            ELSE link.url
        END AS encodedUrl,
        CASE 
            -- For encoded homepage URLs (e.g., Poshmark, The RealReal)
            WHEN link.trackinglink LIKE CONCAT('%', REPLACE(REPLACE(REGEXP_REPLACE(link.url, r'^https?://(www\.)?', ''), ':', '%3A'), '/', '%2F'), '%') 
                THEN REPLACE(REPLACE(REGEXP_REPLACE(link.url, r'^https?://(www\.)?', ''), ':', '%3A'), '/', '%2F')
            -- For non-encoded homepage URLs (e.g., eBay)
            ELSE REGEXP_REPLACE(link.url, r'^https?://(www\.)?', '')
        END AS encodedDomain,
        CASE 
            WHEN program.status_id = 'joined' THEN
                REPLACE(
                    link.trackinglink, 
                    -- Dynamically identify whether the homepage URL is encoded or not
                    CASE 
                        -- For encoded homepage URLs (e.g., Poshmark, The RealReal)
                        WHEN link.trackinglink LIKE CONCAT('%', REPLACE(REPLACE(link.url, ':', '%3A'), '/', '%2F'), '%') 
                            THEN REPLACE(REPLACE(link.url, ':', '%3A'), '/', '%2F')
                        -- For non-encoded homepage URLs (e.g., eBay)
                        ELSE link.url
                    END, 
                    '{{params.encoded_url}}'
                )
            WHEN link.advertiser_id = 'eEJnPPBE' THEN NULL
            ELSE NULL 
        END AS evergreenUrl,
        COALESCE(REGEXP_REPLACE(ascend.trackinglink, r'(url=)[^&]*', 'url={{params.encoded_url}}'), cj.evergreenUrlOverride) AS evergreenUrlOverride,
        cookie,
        -- For commissionRateMax, assume AOV of 100 for fixed fee
        GREATEST(
            -- advertiser-level %, or 0 if missing
            COALESCE(commission.ratio.max, 0),
            -- advertiser-level fixed ÷ AOV, or 0 if missing * 100
            COALESCE(SAFE_DIVIDE(commission.fixed.max, 100), 0) * 100,
            -- program-level %, or 0 if missing
            COALESCE(program.commissions.ratio.max, 0),
            -- program-level fixed ÷ AOV, or 0 if missing * 100
            COALESCE(SAFE_DIVIDE(program.commissions.fixed.max, 100), 0) * 100
        ) AS commissionRateMax,
        networkRank,
        'BOTH' AS device
    FROM `strackr.advertisers` advertisers
    LEFT JOIN `strackr.linkbuilder` link
        ON advertisers.id = link.advertiser_id, 
        UNNEST(programs) AS program
    LEFT JOIN `ops_apps_script.ascend_linkbuilder` ascend
        ON advertisers.source_id = ascend.advertiser_id
        AND advertisers.network_name = 'Ascend by Partnerize'
        AND ascend.trackinglink != 'Link not found'
    LEFT JOIN `affiliate.airflow_cj_text_links` cj
        ON advertisers.source_id = cj.advertiserId
        AND advertisers.network_name = 'CJ Affiliate'
    LEFT JOIN `affiliate.airflow_missing_affiliate_links` missing
        ON advertisers.source_id = missing.advertiser_id
        AND advertisers.network_name = missing.network_name 
    LEFT JOIN ranked_networks net
        ON advertisers.network_name = net.network_name
    WHERE (link.channel_name IS NULL OR link.channel_name != 'Phia.co Instagram')
)

SELECT
    DISTINCT
    CONCAT(
        REPLACE(LOWER(domain), ' ', '_'), '#',
        REPLACE(LOWER(networkName), ' ', '_'), '#',
        REPLACE(LOWER(id), ' ', '_')
    ) AS row_key,
    networkName,
    networkId,
    id AS AdvertiserId,
    name,
    website,
    domainWithPath AS domain,
    commissionRateMax,
    evergreenUrl,
    evergreenUrlOverride,
    cookie AS cookieDurationHours,
    CAST(NULL AS TIMESTAMP) AS evergreenUrlLastTestedAt,
    CAST(NULL AS STRING) AS evergreenUrlTestStatus,
    networkRank,
    device
FROM linkbuilder
WHERE 1=1
    AND programStatus = 'joined'
    AND (linkUrl IS NULL OR domainWithPath = encodedDomain)
    AND COALESCE(evergreenUrl, evergreenUrlOverride) IS NOT NULL

UNION ALL

SELECT 
    CONCAT(REPLACE(LOWER(domain), ' ', '_'), '#', REPLACE(LOWER(networkName), ' ', '_'), '#', REPLACE(LOWER(AdvertiserId), ' ', '_')) AS row_key, 
    networkName, 
    REPLACE(LOWER(networkName), ' ', '_') AS networkId, 
    AdvertiserId, 
    name, 
    website, 
    domain, 
    commissionRateMax, 
    evergreenUrl, 
    evergreenUrlOverride, 
    CAST(cookieDurationHours AS INT64) AS cookieDurationHours, 
    CAST(evergreenUrlLastTestedAt AS TIMESTAMP) AS evergreenUrlLastTestedAt, 
    evergreenUrlTestStatus, 
    networkRank, 
    device
FROM `{{ params.shopmy_table_id }}`

UNION ALL

SELECT 
    CONCAT(REPLACE(LOWER(domain), ' ', '_'), '#', REPLACE(LOWER(networkName), ' ', '_'), '#', REPLACE(LOWER(AdvertiserId), ' ', '_')) AS row_key, 
    networkName, 
    REPLACE(LOWER(networkName), ' ', '_') AS networkId, 
    AdvertiserId, 
    name, 
    website, 
    domain, 
    commissionRateMax, 
    evergreenUrl, 
    evergreenUrlOverride, 
    CAST(cookieDurationHours AS INT64) AS cookieDurationHours, 
    CAST(evergreenUrlLastTestedAt AS TIMESTAMP) AS evergreenUrlLastTestedAt, 
    evergreenUrlTestStatus, 
    networkRank, 
    device
FROM `{{ params.wildfire_table_id }}`

UNION ALL

SELECT 
    CONCAT(REPLACE(LOWER(domain), ' ', '_'), '#', REPLACE(LOWER(networkName), ' ', '_'), '#', REPLACE(LOWER(AdvertiserId), ' ', '_')) AS row_key, 
    networkName, 
    REPLACE(LOWER(networkName), ' ', '_') AS networkId, 
    AdvertiserId, 
    name, 
    website, 
    domain, 
    commissionRateMax, 
    evergreenUrl, 
    evergreenUrlOverride, 
    CAST(cookieDurationHours AS INT64) AS cookieDurationHours, 
    CAST(evergreenUrlLastTestedAt AS TIMESTAMP) AS evergreenUrlLastTestedAt, 
    evergreenUrlTestStatus, 
    networkRank, 
    device
FROM `{{ params.sovrn_table_id }}`

UNION ALL

SELECT 
    CONCAT(REPLACE(LOWER(domain), ' ', '_'), '#', REPLACE(LOWER(networkName), ' ', '_'), '#', REPLACE(LOWER(AdvertiserId), ' ', '_')) AS row_key, 
    networkName, 
    REPLACE(LOWER(networkName), ' ', '_') AS networkId, 
    AdvertiserId, 
    name, 
    website, 
    domain, 
    commissionRateMax, 
    evergreenUrl, 
    evergreenUrlOverride, 
    CAST(cookieDurationHours AS INT64) AS cookieDurationHours, 
    CAST(evergreenUrlLastTestedAt AS TIMESTAMP) AS evergreenUrlLastTestedAt, 
    evergreenUrlTestStatus, 
    networkRank, 
    device
FROM `{{ params.skimlinks_table_id }}`

UNION ALL

SELECT
    CONCAT(REPLACE(LOWER(domain), ' ', '_'), '#', REPLACE(LOWER(networkName), ' ', '_'), '#', REPLACE(LOWER(CONCAT('ext_', TO_HEX(MD5(domain)), '_',device)), ' ', '_')) AS row_key,
    networkName,
    REPLACE(LOWER(networkName), ' ', '_') AS networkId,
    CONCAT('ext_', TO_HEX(MD5(domain)), '_', device) AS AdvertiserId,
    name,
    website,
    domain,
    commissionRateMax,
    evergreenUrl,
    CAST(NULL AS STRING) AS evergreenUrlOverride,
    CAST(NULL AS INT64) AS cookieDurationHours,
    CAST(NULL AS TIMESTAMP) AS evergreenUrlLastTestedAt,
    CAST(NULL AS STRING) AS evergreenUrlTestStatus,
    11 AS networkRank,
    device
FROM `affiliate.airflow_external_links`