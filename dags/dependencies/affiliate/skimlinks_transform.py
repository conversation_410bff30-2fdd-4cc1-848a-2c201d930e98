import pandas as pd
import logging
import requests
import time
from .common import (
    DEFAULT_COOKIE_DURATION,
    DEVICE_BOTH,
    SKIMLINKS_NETWORK_RANK,
    SKIMLINKS_NETWORK_NAME
)
from .constants import (
    SK<PERSON><PERSON>INKS_CLIENT_ID,
    <PERSON><PERSON><PERSON><PERSON><PERSON>_CLIENT_SECRET,
    SKIM<PERSON>INKS_DOMAIN_ID,
    SKIMLINKS_PUBLISHER_ID
)

# Configure logging
logger = logging.getLogger(__name__)

# Skimlinks API Configuration
SKIMLINKS_AUTH_ENDPOINT = "https://authentication.skimapis.com/access_token"
SKIMLINKS_MERCHANTS_ENDPOINT = "https://merchants.skimapis.com/v4/publisher/{publisher_id}/merchants"

# Pagination settings
PAGE_SIZE = 1000
REQUEST_DELAY_SECONDS = 1  # Rate limiting buffer

# Average Order Value for commission calculation
AOV_SKIMLINKS = 100.0

def get_access_token() -> str:
    """Get access token for Skimlinks API authentication."""
    logger.info("Requesting Skimlinks access token")
    
    if not <PERSON><PERSON><PERSON><PERSON><PERSON>_CLIENT_ID or SKIM<PERSON>INKS_CLIENT_ID == "YOUR_<PERSON>IMLINKS_CLIENT_ID":
        logger.error("SKIMLINKS_CLIENT_ID is not set. Please set it before running.")
        raise ValueError("SKIMLINKS_CLIENT_ID not configured.")
    
    if not SKIMLINKS_CLIENT_SECRET or SKIMLINKS_CLIENT_SECRET == "YOUR_SKIMLINKS_CLIENT_SECRET":
        logger.error("SKIMLINKS_CLIENT_SECRET is not set. Please set it before running.")
        raise ValueError("SKIMLINKS_CLIENT_SECRET not configured.")
    
    payload = {
        "client_id": SKIMLINKS_CLIENT_ID,
        "client_secret": SKIMLINKS_CLIENT_SECRET,
        "grant_type": "client_credentials"
    }
    
    headers = {
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.post(SKIMLINKS_AUTH_ENDPOINT, json=payload, headers=headers)
        response.raise_for_status()
        auth_data = response.json()
        access_token = auth_data.get("access_token")
        
        if not access_token:
            raise ValueError("No access token received from Skimlinks API")
        
        logger.info("Successfully obtained Skimlinks access token")
        return access_token
    
    except requests.exceptions.RequestException as e:
        logger.error(f"Failed to get Skimlinks access token: {e}")
        raise

def fetch_skimlinks_merchants(access_token: str) -> list:
    """Fetch all merchants from Skimlinks API with pagination."""
    logger.info("Fetching Skimlinks merchants with pagination")
    
    if not SKIMLINKS_PUBLISHER_ID or SKIMLINKS_PUBLISHER_ID == "YOUR_SKIMLINKS_PUBLISHER_ID":
        logger.error("SKIMLINKS_PUBLISHER_ID is not set. Please set it before running.")
        raise ValueError("SKIMLINKS_PUBLISHER_ID not configured.")
    
    url = SKIMLINKS_MERCHANTS_ENDPOINT.format(publisher_id=SKIMLINKS_PUBLISHER_ID)
    
    headers = {
        "Accept": "application/json"
    }
    
    all_merchants = []
    offset = 0
    has_more = True
    
    while has_more:
        params = {
            "access_token": access_token,
            "limit": PAGE_SIZE,
            "offset": offset
        }
        
        logger.info(f"Fetching page with offset {offset}, limit {PAGE_SIZE}")
        
        try:
            response = requests.get(url, headers=headers, params=params)
            response.raise_for_status()
            data = response.json()
            
            # Extract merchants from response
            merchants = data.get('merchants', [])
            all_merchants.extend(merchants)
            
            # Check pagination info
            has_more = data.get('has_more', False)
            num_returned = data.get('num_returned', 0)
            
            logger.info(f"Fetched {len(merchants)} merchants (total: {len(all_merchants)}, returned: {num_returned})")
            
            if has_more:
                offset += PAGE_SIZE
                logger.info(f"Waiting {REQUEST_DELAY_SECONDS} seconds before next request...")
                time.sleep(REQUEST_DELAY_SECONDS)
            
        except requests.exceptions.RequestException as e:
            logger.error(f"Failed to fetch Skimlinks merchants at offset {offset}: {e}")
            raise
    
    logger.info(f"Successfully fetched {len(all_merchants)} total merchants from Skimlinks")
    return all_merchants

def calculate_commission_rate(merchant: dict) -> float:
    """Calculate commission rate from merchant maximum_rate data."""
    commission_rate = 0.0
    
    # Check for maximum_rate
    if 'maximum_rate' in merchant and merchant['maximum_rate']:
        max_rate = merchant['maximum_rate']
        if isinstance(max_rate, dict):
            # Handle rate object with base_rate and increased_rate
            base_rate = float(max_rate.get('base_rate', 0))
            increased_rate = float(max_rate.get('increased_rate', 0))
            rate_type = max_rate.get('rate_type', '').lower()
            
            if rate_type in ['percentage', 'percent']:
                # Use the higher of base_rate or increased_rate
                commission_rate = max(base_rate, increased_rate)
            else:
                # For non-percentage rates, convert to percentage using AOV
                commission_rate = max(base_rate, increased_rate) / AOV_SKIMLINKS * 100

    # Fallback to calculated_commission_rate if maximum_rate is not available
    if commission_rate == 0.0 and 'calculated_commission_rate' in merchant and merchant['calculated_commission_rate']:
        commission_rate = float(merchant['calculated_commission_rate']) * 100

    return commission_rate

def load_skimlinks_affiliates(**kwargs) -> pd.DataFrame:
    """
    Main function to fetch and transform Skimlinks affiliate data into a standardized DataFrame.
    Similar structure to ShopMy transform.
    """
    try:
        logger.info("Starting Skimlinks affiliate data load")
        
        # Get access token
        access_token = get_access_token()
        
        # Fetch merchant data
        raw_merchants_data = fetch_skimlinks_merchants(access_token)
        
        if not raw_merchants_data:
            logger.warning("No raw data fetched from Skimlinks. Returning empty DataFrame.")
            return pd.DataFrame()
        
        logger.info(f"Processing {len(raw_merchants_data)} merchants from Skimlinks")
        
        # Create evergreen URL template
        if not SKIMLINKS_DOMAIN_ID or SKIMLINKS_DOMAIN_ID == "YOUR_SKIMLINKS_DOMAIN_ID":
            raise Exception('SKIMLINKS_DOMAIN_ID is not set. Please set it before running.')
        else:
            evergreen_url_template = f"https://go.skimresources.com/?id={SKIMLINKS_DOMAIN_ID}" + "&url={{encoded_url}}" + "&xcust={{phia_merchant_product_url_id}}"
        
        all_rows = []
        
        for merchant in raw_merchants_data:
            try:
                advertiser_id = merchant.get('advertiser_id')
                merchant_name = merchant.get('name', '')
                domains = merchant.get('domains', [])
                
                if not advertiser_id or not domains:
                    logger.debug(f"Skipping merchant {merchant_name} due to missing advertiser_id or domains")
                    continue
                
                # Calculate commission rate
                commission_rate = calculate_commission_rate(merchant)
                
                # Create one entry per domain
                for domain in domains:
                    # Clean domain (remove protocol and www if present)
                    website_url = f"https://{domain}"
                    
                    all_rows.append({
                        'networkRank': SKIMLINKS_NETWORK_RANK,
                        'networkName': SKIMLINKS_NETWORK_NAME,
                        'AdvertiserId': f"skimlinks_{advertiser_id}",
                        'Name': merchant_name,
                        'Website': website_url,
                        'domain': domain,
                        'commissionRateMax': round(commission_rate, 2),
                        'evergreenUrl': evergreen_url_template,
                        'evergreenUrlOverride': None,
                        'cookieDurationHours': str(DEFAULT_COOKIE_DURATION),
                        'evergreenUrlLastTestedAt': None,
                        'evergreenUrlTestStatus': None,
                        'device': DEVICE_BOTH
                    })
                    
            except Exception as e:
                logger.warning(f"Error processing merchant {merchant.get('name', 'Unknown')}: {str(e)}")
                continue
        
        df = pd.DataFrame(all_rows)
        logger.info(f"Transformed data, shape: {df.shape}")
        
        # Remove the debug exception
        # raise Exception(df)
        
        return df
        
    except Exception as e:
        logger.error(f"Error processing Skimlinks affiliate data: {str(e)}")
        raise 