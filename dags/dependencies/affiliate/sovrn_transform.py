import pandas as pd
import logging
import requests
import time
import os
from .common import (
    DEFAULT_COOKIE_DURATION,
    DEVICE_BOTH,
    SOVRN_NETWORK_RANK,
    SOVRN_NETWORK_NAME
)
from .constants import SOVRN_SECRET_KEY, SOVRN_REDIRECT_API_KEY

# Configure logging
logger = logging.getLogger(__name__)
logging.basicConfig(level=logging.INFO)

# Sovrn API Configuration
# You can set this as an environment variable for better security.
SOVRN_API_KEY = SOVRN_SECRET_KEY
CAMPAIGN_ID = "6467917"  # Replace if you have a different campaign ID
SOVRN_API_ENDPOINT = "https://viglink.io/merchants/rates/summaries"

PAGE_SIZE = 2500 
REQUEST_DELAY_SECONDS = 10.5 # API rate limit: 1 request every 10 seconds, with 0.5 second buffer

# Sovrn Network Specific Constants
AOV_SOVRN = 100.0  

def fetch_all_sovrn_merchant_data() -> list:
    """Fetches all approved merchant data from the Sovrn API, handling pagination and rate limits."""
    logger.info(f"Starting fetch for all Sovrn merchant data from {SOVRN_API_ENDPOINT}")
    all_merchants = []
    current_page = 1
    total_items = -1

    if not SOVRN_API_KEY or SOVRN_API_KEY == "YOUR_SOVRN_SECRET_KEY":
        logger.error("SOVRN_API_KEY is not set. Please set it before running.")
        raise ValueError("SOVRN_API_KEY not configured. Please replace 'YOUR_SOVRN_SECRET_KEY'.")

    headers = {
        "Authorization": f"secret {SOVRN_API_KEY}",
        "Accept": "application/json",
        "Content-Type": "application/json"
    }
    params = {
        "campaignId": CAMPAIGN_ID
    }

    while True:
        payload = {
            "page": current_page,
            "pageSize": PAGE_SIZE,
        }
        logger.info(f"Fetching page {current_page} with pageSize {PAGE_SIZE}...")

        try:
            response = requests.post(SOVRN_API_ENDPOINT, headers=headers, params=params, json=payload)
            response.raise_for_status()  # Raise an exception for HTTP errors (4xx or 5xx)
            data = response.json()
        except requests.exceptions.RequestException as e:
            logger.error(f"API request failed for page {current_page}: {e}")
            # Depending on the error, you might want to retry or break
            break 

        page_results = data.get("results", [])
        all_merchants.extend(page_results)

        if total_items == -1: # First request, set total_items
            total_items = data.get("pagination", {}).get("totalItems", 0)
            logger.info(f"Total items to fetch: {total_items}")

        logger.info(f"Fetched {len(page_results)} records from page {current_page}. Total fetched so far: {len(all_merchants)}.")

        if not page_results or len(all_merchants) >= total_items:
            logger.info("All pages fetched or no more results.")
            break

        current_page += 1
        logger.info(f"Waiting {REQUEST_DELAY_SECONDS} seconds before next request due to rate limit...")
        time.sleep(REQUEST_DELAY_SECONDS)
    
    logger.info(f"Finished fetching. Total merchants retrieved: {len(all_merchants)}")
    return all_merchants

def transform_sovrn_data_to_standard_format(all_merchants_data: list) -> pd.DataFrame:
    """Transforms raw Sovrn merchant data into a single standardized DataFrame."""
    if not all_merchants_data:
        logger.info("No merchant data provided for transformation.")
        return pd.DataFrame()

    logger.info(f"Transforming {len(all_merchants_data)} Sovrn merchant records into standard format...")
    transformed_records = []

    for merchant in all_merchants_data:
        merchant_id = merchant.get('groupId')
        if merchant_id == "228" or merchant_id == 228:
            print(merchant)
        merchant_name = merchant.get('name')
        sovrn_details = merchant.get('sovrn', {})

        # Determine primary domain and website URL
        primary_domain = None
        website_url = None
        # Prioritize domains from CPA entries
        if sovrn_details and 'cpa' in sovrn_details and isinstance(sovrn_details['cpa'], list):
            for cpa_geo_entry in sovrn_details['cpa']:
                if isinstance(cpa_geo_entry, dict) and 'domains' in cpa_geo_entry and cpa_geo_entry['domains']:
                    primary_domain = cpa_geo_entry['domains'][0].lower()
                    break 
        # If not found in CPA, try CPC
        if not primary_domain and sovrn_details and 'cpc' in sovrn_details:
            cpc_info = sovrn_details.get('cpc', {})
            if isinstance(cpc_info, dict) and 'domains' in cpc_info and cpc_info['domains']:
                primary_domain = cpc_info['domains'][0].lower()
        
        if primary_domain:
            website_url = f"https://{primary_domain}"

        # Calculate commissionRateMax
        max_commission_pct = 0.0
        if sovrn_details and 'cpa' in sovrn_details and isinstance(sovrn_details['cpa'], list):
            for cpa_geo_entry in sovrn_details['cpa']:
                if isinstance(cpa_geo_entry, dict) and 'calculatedCommissionRate' in cpa_geo_entry and isinstance(cpa_geo_entry['calculatedCommissionRate'], float):
                    max_commission_pct = max(max_commission_pct, float(cpa_geo_entry['calculatedCommissionRate']) * 100)
                    continue
                if isinstance(cpa_geo_entry, dict) and 'rates' in cpa_geo_entry and isinstance(cpa_geo_entry['rates'], list):
                    for rate_detail in cpa_geo_entry['rates']:
                        if not isinstance(rate_detail, dict):
                            continue
                        current_rate_value = float(rate_detail.get('currentRate', 0.0))
                        rate_format = str(rate_detail.get('rateFormat', '')).lower()
                        aov_merchant = cpa_geo_entry.get('averageOrderValue', AOV_SOVRN)
                        commission_pct = 0.0
                        if rate_format == 'percent':
                            commission_pct = current_rate_value
                        elif rate_format in ['fixed', 'usd', 'eur', 'gbp', 'cad', 'aud']:
                            if aov_merchant > 0:
                                commission_pct = (current_rate_value / aov_merchant)
                            else:
                                commission_pct = 0
                        
                        max_commission_pct = max(max_commission_pct, commission_pct)
        
        # Construct Evergreen URL
        evergreen_url_template = f"https://redirect.viglink.com?key={SOVRN_REDIRECT_API_KEY}" + "&u={{encoded_url}}"
        if not SOVRN_REDIRECT_API_KEY or SOVRN_REDIRECT_API_KEY == "YOUR_SOVRN_SECRET_KEY":
            logger.warning(f"SOVRN_REDIRECT_API_KEY for merchant {merchant_name} ({merchant_id}) is a placeholder. Evergreen URL will not be functional.")

        transformed_records.append({
            'networkRank': SOVRN_NETWORK_RANK,
            'networkName': SOVRN_NETWORK_NAME,
            'AdvertiserId': f"sovrn_{merchant_id}",
            'Name': merchant_name,
            'Website': website_url,
            'domain': primary_domain,
            'commissionRateMax': round(max_commission_pct, 2),
            'evergreenUrl': evergreen_url_template,
            'evergreenUrlOverride': None,
            'cookieDurationHours': str(DEFAULT_COOKIE_DURATION),
            'evergreenUrlLastTestedAt': None,
            'evergreenUrlTestStatus': None,
            'device': DEVICE_BOTH
        })
    
    df_standardized = pd.DataFrame(transformed_records)
    # remove rows with null values for website and domain
    df_standardized = df_standardized[df_standardized['Website'].notna() & df_standardized['domain'].notna() & df_standardized['AdvertiserId'].notna()]
    logger.info(f"Sovrn data transformed into standard format. Created {len(df_standardized)} records.")
    return df_standardized

def load_sovrn_affiliates() -> pd.DataFrame:
    """
    Main function to fetch and transform Sovrn affiliate data into a single standardized DataFrame.
    """
    logger.info("Starting Sovrn affiliate data processing...")

    try:
        raw_merchant_data = fetch_all_sovrn_merchant_data()
    except ValueError as e: # Specifically for API key not set
        logger.error(f"Critical error fetching Sovrn data: {e}")
        return pd.DataFrame() # Return empty DataFrame
    except requests.exceptions.RequestException as e:
        logger.error(f"API Request error during Sovrn data fetching: {e}")
        return pd.DataFrame()
    except Exception as e:
        logger.error(f"An unexpected error occurred during Sovrn data fetching: {e}")
        return pd.DataFrame()

    if not raw_merchant_data:
        logger.warning("No raw data fetched from Sovrn. Returning empty DataFrame.")
        return pd.DataFrame()

    df_transformed_standard = transform_sovrn_data_to_standard_format(raw_merchant_data)

    logger.info("Sovrn affiliate data processing completed.")
    return df_transformed_standard

